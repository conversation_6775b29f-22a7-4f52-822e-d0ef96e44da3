# Localized Validation System for DigiflowAPI

## Overview
This document explains how to use the new localized validation system that automatically handles multiple languages (Turkish and English) based on HTTP headers.

## Architecture

### 1. IResourceLocalizationService
- **Interface**: `DigiflowAPI.Application.Services.Interfaces.IResourceLocalizationService`
- **Implementation**: `DigiflowAPI.Application.Services.ResourceLocalizationService`
- **Purpose**: Manages resource loading and localization for validation messages

### 2. LocalizedValidator<T> Base Class
- **Location**: `DigiflowAPI.Application.Validators.Base.LocalizedValidator<T>`
- **Purpose**: Base class for all validators that need localization support
- **Features**:
  - Automatic language detection from HTTP headers
  - Easy resource message retrieval
  - Fallback message support

### 3. Resource Files
- **Location**: `DigiflowAPI.Resources/Workflow/`
- **Format**: 
  - English: `[WorkflowType].resx`
  - Turkish: `[WorkflowType].tr.resx`
- **Example**: `CreateWorkflow.resx` and `CreateWorkflow.tr.resx`

## How to Use

### Step 1: Create Resource Files
1. Add English resource file: `[WorkflowType].resx`
2. Add Turkish resource file: `[WorkflowType].tr.resx`
3. Add resource keys with appropriate translations

Example for RejectWorkflow:
```xml
<!-- RejectWorkflow.resx (English) -->
<data name="requiredComment" xml:space="preserve">
  <value>Comment is required for rejection</value>
</data>

<!-- RejectWorkflow.tr.resx (Turkish) -->
<data name="requiredComment" xml:space="preserve">
  <value>Red için yorum zorunludur</value>
</data>
```

### Step 2: Create Validator Class
Inherit from `LocalizedValidator<T>` instead of `AbstractValidator<T>`:

```csharp
public class RejectWorkflowRequestDtoValidator : LocalizedValidator<RejectWorkflowRequestDto>
{
    protected override string ResourceCategory => "RejectWorkflow";

    public RejectWorkflowRequestDtoValidator(
        IHttpContextAccessor httpContextAccessor,
        IServiceProvider serviceProvider) : base(httpContextAccessor, serviceProvider)
    {
        RuleFor(x => x.Comment)
            .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredComment", "Comment is required for rejection"));
    }
}
```

### Step 3: Register in DI Container
The `IResourceLocalizationService` is already registered in both WebApi and MobileApi projects.

## Language Detection Priority

The system detects language from HTTP context in the following priority order:

1. **UserLanguage** item in HttpContext.Items (set by middleware)
2. **Accept-Language** header
3. **X-Language** or **Language** custom headers
4. **lang** or **language** query parameters
5. **Default**: Turkish ("tr")

## Example HTTP Requests

### Using Accept-Language Header
```http
GET /api/workflows
Accept-Language: en-US,en;q=0.9
```

### Using Custom Header
```http
GET /api/workflows
X-Language: tr
```

### Using Query Parameter
```http
GET /api/workflows?lang=en
```

## Common Resource Keys

### Standard Validation Keys
- `required[FieldName]`: "[Field] is required" / "[Field] zorunludur"
- `invalid[FieldName]`: "Invalid [field]" / "Geçersiz [field]"
- `[field]TooLong`: "[Field] is too long" / "[Field] çok uzun"
- `[field]TooShort`: "[Field] is too short" / "[Field] çok kısa"

### Common Workflow Keys
- `requiredWorkflowName`: "Workflow name is required" / "Akış adı zorunludur"
- `requiredInstanceId`: "Instance ID is required" / "Talep ID zorunludur"
- `invalidInstanceId`: "Instance ID must be greater than 0" / "Talep ID 0'dan büyük olmalıdır"
- `requiredComment`: "Comment is required" / "Yorum zorunludur"

## Migration Guide

### Converting Existing Validators

**Before:**
```csharp
public class MyValidator : AbstractValidator<MyDto>
{
    public MyValidator(IHttpContextAccessor httpContextAccessor)
    {
        string language = httpContextAccessor.HttpContext.Items["UserLanguage"]?.ToString() ?? "tr";
        
        RuleFor(x => x.Field)
            .NotEmpty().WithMessage("Field is required"); // Hardcoded message
    }
}
```

**After:**
```csharp
public class MyValidator : LocalizedValidator<MyDto>
{
    protected override string ResourceCategory => "MyWorkflowType";

    public MyValidator(IHttpContextAccessor httpContextAccessor, IServiceProvider serviceProvider) 
        : base(httpContextAccessor, serviceProvider)
    {
        RuleFor(x => x.Field)
            .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredField", "Field is required"));
    }
}
```

## Best Practices

1. **Resource Naming**: Use consistent naming patterns for resource keys
2. **Fallback Messages**: Always provide fallback messages in English
3. **Resource Categories**: Use clear, descriptive category names that match workflow types
4. **Key Naming**: Use camelCase for resource keys (e.g., `requiredField`, `invalidDate`)
5. **Parameter Support**: Use string formatting for dynamic messages: `GetLocalizedMessage("userNotFound", "User {0} not found", userId)`

## Examples

### Complete Validator Example
```csharp
public class CreateWorkflowRequestDtoValidator : LocalizedValidator<CreateWorkflowRequestDto>
{
    protected override string ResourceCategory => "CreateWorkflow";
    private readonly IServiceProvider _serviceProvider;

    public CreateWorkflowRequestDtoValidator(
        IHttpContextAccessor httpContextAccessor,
        IServiceProvider serviceProvider) : base(httpContextAccessor, serviceProvider)
    {
        _serviceProvider = serviceProvider;

        RuleFor(x => x.WorkflowName)
            .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredWorkflowName"));

        RuleFor(x => x.EntityJson)
            .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredEntityJson"))
            .Custom((entityJson, context) => {
                try {
                    // Custom validation logic
                } catch (Exception ex) {
                    context.AddFailure("EntityJson", 
                        GetLocalizedMessage("jsonParseError", "Error parsing JSON: {0}", ex.Message));
                }
            });
    }
}
```

### Resource File Example
```xml
<!-- CreateWorkflow.resx -->
<data name="requiredWorkflowName" xml:space="preserve">
  <value>Workflow name is required</value>
</data>
<data name="requiredEntityJson" xml:space="preserve">
  <value>Entity JSON is required</value>
</data>
<data name="jsonParseError" xml:space="preserve">
  <value>Error parsing JSON: {0}</value>
</data>

<!-- CreateWorkflow.tr.resx -->
<data name="requiredWorkflowName" xml:space="preserve">
  <value>Akış adı zorunludur</value>
</data>
<data name="requiredEntityJson" xml:space="preserve">
  <value>Varlık JSON'u zorunludur</value>
</data>
<data name="jsonParseError" xml:space="preserve">
  <value>JSON ayrıştırma hatası: {0}</value>
</data>
```

## Troubleshooting

### Common Issues

1. **Resource Not Found**: Check that resource files exist and resource keys are correct
2. **Wrong Language**: Verify HTTP headers and language detection logic
3. **Missing Fallback**: Always provide fallback messages for better debugging
4. **DI Issues**: Ensure `IResourceLocalizationService` is registered in the DI container

### Debugging

Enable logging to see resource loading details:
```csharp
// In appsettings.json
{
  "Logging": {
    "LogLevel": {
      "DigiflowAPI.Application.Services.ResourceLocalizationService": "Debug"
    }
  }
}
```
