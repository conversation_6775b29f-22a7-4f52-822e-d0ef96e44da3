# Localized Validation System for DigiflowAPI

## Overview
This document explains the comprehensive localized validation system that automatically handles multiple languages (Turkish and English) based on HTTP headers and integrates with the existing workflow configuration system.

## Architecture

### 1. IResourceLocalizationService
- **Interface**: `DigiflowAPI.Application.Services.Interfaces.IResourceLocalizationService`
- **Implementation**: `DigiflowAPI.Application.Services.ResourceLocalizationService`
- **Purpose**: Manages resource loading and localization for validation messages
- **Registration**: Automatically registered in both WebApi and MobileApi DI containers

### 2. LocalizedValidator<T> Base Class
- **Location**: `DigiflowAPI.Application.Validators.Base.LocalizedValidator<T>`
- **Purpose**: Base class for simple DTO validators that need localization support
- **Use Case**: Basic structural validation (e.g., RejectWorkflowRequestDtoValidator)

### 3. BaseWorkflowValidator<T> Enhanced
- **Location**: `DigiflowAPI.Application.Validators.Workflow.Base.BaseWorkflowValidator<T>`
- **Purpose**: Base class for complex workflow validators with configuration-based rules
- **Integration**: Now includes full localization support with language detection
- **Use Case**: Dynamic workflow validation (e.g., DynamicWorkflowValidator)

### 4. DynamicWorkflowValidator<T>
- **Location**: `DigiflowAPI.Application.Validators.Workflow.Factory.DynamicWorkflowValidator<T>`
- **Purpose**: Applies workflow-specific validation rules based on configuration
- **Integration**: Now uses localized messages from workflow-specific or Common resources

### 5. Resource Files Structure
```
DigiflowAPI.Resources/Workflow/
├── Common.resx (English common messages)
├── Common.tr.resx (Turkish common messages)
├── CreateWorkflow.resx (English workflow-specific)
├── CreateWorkflow.tr.resx (Turkish workflow-specific)
├── RejectWorkflow.resx (English)
├── RejectWorkflow.tr.resx (Turkish)
└── ... (other workflow types)
```

## How the Two-Level Validation System Works

### Level 1: Basic DTO Validation
**Purpose**: Validates basic structure and data types
**Examples**: `RejectWorkflowRequestDtoValidator`, `ForwardWorkflowRequestDtoValidator`
**Scope**: 
- Required primitive fields (WorkflowName, InstanceId, LoginUserId)
- Basic data type validation (ID > 0, etc.)
- NOT workflow-specific business rules

```csharp
// Example: Basic DTO Validator
public class RejectWorkflowRequestDtoValidator : LocalizedValidator<RejectWorkflowRequestDto>
{
    protected override string ResourceCategory => "Common";

    public RejectWorkflowRequestDtoValidator(IHttpContextAccessor httpContextAccessor, IServiceProvider serviceProvider) 
        : base(httpContextAccessor, serviceProvider)
    {
        // Only basic structure validation
        RuleFor(x => x.WorkflowName)
            .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredWorkflowName"));

        RuleFor(x => x.InstanceId)
            .GreaterThan(0).WithMessage(_ => GetLocalizedMessage("invalidInstanceId"));

        // Note: Comment validation is NOT here - it's workflow-specific
    }
}
```

### Level 2: Configuration-Based Workflow Validation
**Purpose**: Validates workflow-specific business rules
**Implementation**: `DynamicWorkflowValidator<T>` + Configuration classes
**Scope**:
- Workflow-specific field requirements (e.g., RejectReason for contracts)
- State-specific validation rules
- Complex business logic validation

```csharp
// Example: Configuration defines workflow-specific rules
["Reject"] = new ActionValidationRules
{
    RequiredFields = new List<RequiredFieldRule>
    {
        new() { 
            FieldName = "Comment", 
            ErrorKey = "rejectReasonRequired", 
            DefaultMessage = "Reject reason is required." 
        }
    }
}
```

## Language Detection Priority

The system detects language from HTTP context in the following priority order:

1. **UserLanguage** item in HttpContext.Items (set by middleware)
2. **Accept-Language** header (automatically parses and detects tr/en)
3. **X-Language** or **Language** custom headers
4. **lang** or **language** query parameters
5. **Default**: Turkish ("tr")

## Resource Resolution Strategy

When a validation message is needed:

1. **Try Workflow-Specific Resources**: e.g., `CreateWorkflow.resx` → `requiredEntityJson`
2. **Fallback to Common Resources**: `Common.resx` → `requiredEntityJson`
3. **Fallback to Default Message**: English hardcoded message
4. **Log Warning**: If resource not found for debugging

## Example HTTP Requests

### Using Accept-Language Header
```http
POST /api/workflows/reject
Accept-Language: en-US,en;q=0.9
Content-Type: application/json

{
  "workflowName": "contract",
  "instanceId": 123,
  "loginUserId": 456,
  "comment": "Rejected due to missing documents"
}
```

### Using Custom Header
```http
POST /api/workflows/reject
X-Language: tr
Content-Type: application/json

{
  "workflowName": "contract",
  "instanceId": 123,
  "loginUserId": 456,
  "comment": "Eksik belgeler nedeniyle reddedildi"
}
```

## Common Resource Keys

### Structural Validation (Common.resx)
```xml
<!-- Basic Requirements -->
<data name="requiredWorkflowName"><value>Workflow name is required</value></data>
<data name="requiredInstanceId"><value>Instance ID is required</value></data>
<data name="requiredLoginUserId"><value>Login User ID is required</value></data>

<!-- Invalid Values -->
<data name="invalidInstanceId"><value>Instance ID must be greater than 0</value></data>
<data name="invalidLoginUserId"><value>Login User ID must be greater than 0</value></data>
<data name="invalidWorkflowType"><value>Invalid workflow type: {0}</value></data>

<!-- Action-Specific (handled by configuration) -->
<data name="rejectReasonRequired"><value>Reject reason is required</value></data>
<data name="forwardLoginIdRequired"><value>Forward login ID is required</value></data>
<data name="cancelReasonRequired"><value>Cancel reason is required</value></data>
```

## Workflow Configuration Integration

### Configuration Example
```csharp
// In ContractConfiguration.cs
public class ContractConfiguration : IWorkflowConfiguration
{
    public Dictionary<string, ActionValidationRules> ActionRules => new()
    {
        ["Reject"] = new ActionValidationRules
        {
            RequiredFields = new List<RequiredFieldRule>
            {
                new() { 
                    FieldName = "Comment", 
                    ErrorKey = "rejectReasonRequired", 
                    DefaultMessage = "Reject reason is required for contracts." 
                }
            }
        },
        ["Forward"] = new ActionValidationRules
        {
            RequiredFields = new List<RequiredFieldRule>
            {
                new() { 
                    FieldName = "ForwardLoginId", 
                    ErrorKey = "forwardLoginIdRequired", 
                    DefaultMessage = "Forward target user is required." 
                }
            }
        }
    };
}
```

### How It's Applied
1. **DTO Validator** validates basic structure
2. **DynamicWorkflowValidator** is created with workflow type, action, and state
3. **Configuration** provides the specific rules for that context
4. **Localized messages** are retrieved based on detected language

## Migration Guide for Existing Validators

### Before: Complex DTO Validator
```csharp
public class RejectWorkflowRequestDtoValidator : AbstractValidator<RejectWorkflowRequestDto>
{
    public RejectWorkflowRequestDtoValidator()
    {
        RuleFor(x => x.WorkflowName).NotEmpty().WithMessage("Workflow name is required");
        RuleFor(x => x.Comment).NotEmpty().WithMessage("Comment is required"); // Wrong!
        // Comment requirements vary by workflow type!
    }
}
```

### After: Separated Concerns
```csharp
// 1. Simplified DTO Validator (structure only)
public class RejectWorkflowRequestDtoValidator : LocalizedValidator<RejectWorkflowRequestDto>
{
    protected override string ResourceCategory => "Common";
    
    public RejectWorkflowRequestDtoValidator(IHttpContextAccessor httpContextAccessor, IServiceProvider serviceProvider) 
        : base(httpContextAccessor, serviceProvider)
    {
        RuleFor(x => x.WorkflowName)
            .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredWorkflowName"));
        // Comment validation is in configuration!
    }
}

// 2. Workflow-specific rules in configuration
["Reject"] = new ActionValidationRules
{
    RequiredFields = new List<RequiredFieldRule>
    {
        new() { FieldName = "Comment", ErrorKey = "rejectReasonRequired" }
    }
}
```

## Best Practices

### 1. Validator Responsibility
- **DTO Validators**: Only basic structure and data types
- **Configuration**: Workflow-specific business rules
- **Resources**: All user-facing messages

### 2. Resource Organization
- **Common.resx**: Messages used across multiple workflows
- **{WorkflowType}.resx**: Workflow-specific messages
- **Consistent naming**: Use same key names across workflow types when possible

### 3. Configuration Alignment
- **Mirror React schemas**: Keep C# configurations aligned with React validation schemas
- **State-specific rules**: Use state-specific rules for approve workflows
- **Default fallbacks**: Always provide default rules for unknown states

### 4. Error Key Naming
```csharp
// Good naming conventions
"requiredFieldName"     // required + field name
"invalidFieldName"      // invalid + field name  
"fieldNameTooLong"      // field name + constraint
"fieldNameMustBe..."    // field name + specific rule
```

## Debugging and Troubleshooting

### Enable Detailed Logging
```json
// In appsettings.json
{
  "Logging": {
    "LogLevel": {
      "DigiflowAPI.Application.Services.ResourceLocalizationService": "Debug",
      "DigiflowAPI.Application.Validators.Workflow.Base.BaseWorkflowValidator": "Debug"
    }
  }
}
```

### Common Issues

1. **Message Not Localized**: Check resource file exists and key is correct
2. **Wrong Language**: Verify Accept-Language header or X-Language header
3. **Missing Configuration**: Ensure workflow configuration defines the action rules
4. **Resource Loading Error**: Check that DigiflowAPI.Resources assembly is loaded

### Validation Flow Debugging
```csharp
// The validation happens in this order:
1. HTTP Request → Language Detection (Accept-Language, etc.)
2. DTO Validator → Basic Structure Validation (using Common resources)
3. DynamicWorkflowValidator → Configuration-Based Rules (using workflow-specific resources)
4. Resource Resolution → Workflow Resources → Common Resources → Fallback Message
```

## Example: Complete Validation Flow

### Request
```http
POST /api/workflows/reject
Accept-Language: tr-TR,tr;q=0.9
Content-Type: application/json

{
  "workflowName": "contract",
  "instanceId": 0,  // Invalid!
  "loginUserId": 456,
  "comment": ""     // Required for contracts!
}
```

### Validation Process
1. **Language Detection**: Turkish detected from Accept-Language
2. **DTO Validation**: `RejectWorkflowRequestDtoValidator`
   - `InstanceId = 0` → fails `invalidInstanceId` → "Talep ID 0'dan büyük olmalıdır"
3. **Configuration Validation**: `DynamicWorkflowValidator`
   - Contract configuration requires `Comment` for Reject
   - Empty comment → fails `rejectReasonRequired` → "Red nedeni zorunludur"

### Response
```json
{
  "errors": {
    "InstanceId": ["Talep ID 0'dan büyük olmalıdır"],
    "Comment": ["Red nedeni zorunludur"]
  }
}
```

This system ensures that all validation messages are properly localized while maintaining clean separation between structural validation and business rule validation!
