using AutoMapper;
using DigiflowAPI.Application.DTOs.Common;
using DigiflowAPI.Application.DTOs.Inbox;
using DigiflowAPI.Application.DTOs.Organization;
using DigiflowAPI.Application.DTOs.User;
using DigiflowAPI.Application.DTOs.Workflow.Delegation;
using DigiflowAPI.Application.DTOs.Workflow.XML.Definition;
using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Application.DTOs;
using DigiflowAPI.Application.DTOs.Workflow.History;
using DigiflowAPI.Domain.Entities.History;
using DigiflowAPI.Application.Commands.FileUpload;
using DigiflowAPI.Domain.Entities.Workflow;
using DigiflowAPI.Application.DTOs.Workflow;

namespace DigiflowAPI.Application.Mappers
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<DelegationWorkflowEntity, DelegationWorkflowDto>();
            CreateMap<DelegationWorkflowDto, DelegationWorkflowEntity>();

            // Add mapping for DelegationWorkflowEntity to EndDelegationTableDto
            CreateMap<DelegationWorkflowEntity, EndDelegationTableDto>()
                .ForMember(dest => dest.WfDelegationId, opt => opt.MapFrom(src => src.DelegationRequestId))
                .ForMember(dest => dest.Name, opt => opt.Ignore()) // This should be mapped from workflow definition name if needed
                .ForMember(dest => dest.DelegationOwnerRefId, opt => opt.MapFrom(src => src.OwnerLoginId))
                .ForMember(dest => dest.DelegationRequestId, opt => opt.MapFrom(src => src.DelegationRequestId))
                .ForMember(dest => dest.DelegateRefId, opt => opt.MapFrom(src => src.DelegatedLoginId))
                .ForMember(dest => dest.OwnerNameSurname, opt => opt.MapFrom(src => src.OwnerLoginName))
                .ForMember(dest => dest.NameSurname, opt => opt.MapFrom(src => src.ActiveLoginName))
                .ForMember(dest => dest.DelegationStartDate, opt => opt.MapFrom(src => src.StartTime))
                .ForMember(dest => dest.DelegationEndDate, opt => opt.MapFrom(src => src.EndTime))
                .ForMember(dest => dest.WorkflowDefId, opt => opt.MapFrom(src => src.WorkflowDefId));

            CreateMap<DpHrDeps, SelectOptionDto>()
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Id.ToString()))
                .ForMember(dest => dest.Label, opt => opt.MapFrom(src => src.Bolum))
                .ForMember(dest => dest.LabelEn, opt => opt.MapFrom(src => src.DepsEn));

            CreateMap<SelectOptionDto, DpHrDeps>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => long.Parse(src.Value)))
                .ForMember(dest => dest.Bolum, opt => opt.MapFrom(src => src.Label))
                .ForMember(dest => dest.DepsEn, opt => opt.MapFrom(src => src.LabelEn));

            CreateMap<SelectOptionDto, OrgTreeSelectOptionDto>()
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value))
                .ForMember(dest => dest.Label, opt => opt.MapFrom(src => src.Label))
                .ForMember(dest => dest.LabelEn, opt => opt.MapFrom(src => src.LabelEn));

            CreateMap<OrgTreeSelectOptionDto, SelectOptionDto>()
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value))
                .ForMember(dest => dest.Label, opt => opt.MapFrom(src => src.Label))
                .ForMember(dest => dest.LabelEn, opt => opt.MapFrom(src => src.LabelEn));


            CreateMap<VwWorkflowHistory, WorkflowHistoryDto>();
            CreateMap<DpHrUsers, DPHRUsersDto>();
            CreateMap<DpHrDeps, DepartmentDto>();
            CreateMap<SelectOptionDto, DepartmentSelectDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Value))
            .ForMember(dest => dest.Bolum, opt => opt.MapFrom(src => src.Label))
            .ForMember(dest => dest.BolumEn, opt => opt.MapFrom(src => src.LabelEn));
            CreateMap<VwUserInformation, UserDto>()
            .ForMember(dest => dest.DpHrUsers, opt => opt.MapFrom(src => src.DpHrUsers));
            CreateMap<DepsPath, DepsPathSelectDto>()
            .ForMember(dest => dest.DepsTr, opt => opt.MapFrom(src => src.DepsTr))
            .ForMember(dest => dest.DepsEn, opt => opt.MapFrom(src => src.DepsEn));
            CreateMap<WfXmlDefinition, WorkflowXmlDefinitionDto>();
            CreateMap<WfXmlDefinition, WorkflowXmlDefinitionSelectDto>()
            .ForMember(dest => dest.FlowName, opt => opt.MapFrom(src => src.Flowname))
            .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value));

            // Add mapping for WorkflowSelectEntity to WorkflowXmlDefinitionSelectDto
            CreateMap<WorkflowSelectEntity, WorkflowXmlDefinitionSelectDto>()
                .ForMember(dest => dest.FlowName, opt => opt.MapFrom(src => src.FlowName))
                .ForMember(dest => dest.FlowNameEn, opt => opt.MapFrom(src => src.FlowNameEn))
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value));

            // Add mapping for FWfWorkflowDef to WorkflowXmlDefinitionSelectDto
            CreateMap<DigiflowAPI.Domain.Entities.Framework.FWfWorkflowDef, WorkflowXmlDefinitionSelectDto>()
            .ForMember(dest => dest.FlowName, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.FlowNameEn, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.WfWorkflowDefId.ToString()));

            CreateMap<UploadFileCommand, UploadedFile>();


            CreateMap<TbWorkflowTaskView, SuspendedInboxDTO>()
            .ForMember(dest => dest.wfInsId, opt => opt.MapFrom(src => src.WorkflowInstanceId.ToString()))
            .ForMember(dest => dest.wfInstanceLink, opt => opt.MapFrom(src => $"{src.TaskScreen}?wfInstanceId={src.WorkflowInstanceId}"))
            .ForMember(dest => dest.wfOwner, opt => opt.MapFrom(src => src.OwnerLoginId.ToString()))
            .ForMember(dest => dest.wfLastModifiedBy, opt => opt.MapFrom(src => src.TaskOwnerId.ToString() ?? ""))
            .ForMember(dest => dest.wfDate, opt => opt.MapFrom(src => src.StartTime.ToString("dd.MM.yyyy HH:mm")));

            // Map InboxDto to Inbox entity
            CreateMap<InboxDto, Inbox>()
            .ForMember(dest => dest.WfInsId, opt => opt.MapFrom(src => src.WfInsId))
            .ForMember(dest => dest.FlowName, opt => opt.MapFrom(src => src.FlowName))
            .ForMember(dest => dest.StateName, opt => opt.MapFrom(src => src.StateName))
            .ForMember(dest => dest.Route, opt => opt.MapFrom(src => src.Route))
            .ForMember(dest => dest.OwnerLoginId, opt => opt.MapFrom(src => src.LastLoginId))
            .ForMember(dest => dest.WfWorkflowDefId, opt => opt.MapFrom(src => src.WfWorkflowDefId))
            .ForMember(dest => dest.LastLoginId, opt => opt.MapFrom(src => src.LastLoginId))
            .ForMember(dest => dest.WfDate, opt => opt.MapFrom(src => src.WfDate))
            .ForMember(dest => dest.StartTime, opt => opt.MapFrom(src => src.WfDate))
            .ForMember(dest => dest.TaskScreen, opt => opt.MapFrom(src => src.WfInstanceLink))
            .ForMember(dest => dest.WfInstanceDef, opt => opt.MapFrom(src => src.WfInstanceDef))
            .ForMember(dest => dest.EntityRefId, opt => opt.Ignore())
            .ForMember(dest => dest.WfActionStatusTypeCd, opt => opt.Ignore())
            .ForMember(dest => dest.WfActionTaskInstanceId, opt => opt.Ignore())
            .ForMember(dest => dest.WfActionTaskStatusTCd, opt => opt.Ignore())
            .ForMember(dest => dest.WfActionDefId, opt => opt.Ignore())
            .ForMember(dest => dest.WfOwner, opt => opt.Ignore())
            .ForMember(dest => dest.WfLastModifiedBy, opt => opt.Ignore())
            .ForMember(dest => dest.WfLastModifiedByNom, opt => opt.Ignore());

            // Mapping for OrganizationHierarchy domain entity to OrganizationSchemaParamsDto
            CreateMap<DigiflowAPI.Domain.Entities.Organization.OrganizationHierarchy, OrganizationSchemaParamsDto>()
                .ForMember(dest => dest.DepsPath, opt => opt.MapFrom(src => src.DepsPath))
                .ForMember(dest => dest.Departments, opt => opt.MapFrom(src =>
                    src.Departments.Select(d => new OrgTreeSelectOptionDto
                    {
                        Value = d.Id.ToString(),
                        Label = d.Bolum,
                        LabelEn = d.DepsEn
                    })))
                .ForMember(dest => dest.Users, opt => opt.MapFrom(src =>
                    src.Users.Select(u => new OrgTreeSelectOptionDto
                    {
                        Value = u.LoginId.ToString(),
                        Label = u.NameSurname,
                        LabelEn = u.NameSurname
                    })))
                .ForMember(dest => dest.SelectedDepartment, opt => opt.Ignore())
                .ForMember(dest => dest.Divisions, opt => opt.Ignore())
                .ForMember(dest => dest.SelectedDivision, opt => opt.Ignore())
                .ForMember(dest => dest.Units, opt => opt.Ignore())
                .ForMember(dest => dest.SelectedUnit, opt => opt.Ignore())
                .ForMember(dest => dest.Teams, opt => opt.Ignore())
                .ForMember(dest => dest.SelectedTeam, opt => opt.Ignore())
                .ForMember(dest => dest.SubTeams, opt => opt.Ignore())
                .ForMember(dest => dest.SelectedSubTeams, opt => opt.Ignore());
        }
    }
}