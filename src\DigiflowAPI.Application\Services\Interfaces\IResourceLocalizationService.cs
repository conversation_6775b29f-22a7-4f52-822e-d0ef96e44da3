using System.Globalization;

namespace DigiflowAPI.Application.Services.Interfaces
{
    public interface IResourceLocalizationService
    {
        /// <summary>
        /// Gets a localized string for the specified resource category and key
        /// </summary>
        /// <param name="category">The resource category (e.g., "CreateWorkflow", "ApprovalWorkflow")</param>
        /// <param name="key">The resource key</param>
        /// <param name="culture">The culture to use for localization. If null, uses current culture</param>
        /// <param name="fallbackMessage">Fallback message if resource not found</param>
        /// <param name="args">Optional format arguments</param>
        /// <returns>Localized string</returns>
        string GetString(string category, string key, CultureInfo culture = null, string fallbackMessage = null, params object[] args);

        /// <summary>
        /// Gets a localized string for the specified resource category and key using language code
        /// </summary>
        /// <param name="category">The resource category (e.g., "CreateWorkflow", "ApprovalWorkflow")</param>
        /// <param name="key">The resource key</param>
        /// <param name="languageCode">Language code (e.g., "tr", "en")</param>
        /// <param name="fallbackMessage">Fallback message if resource not found</param>
        /// <param name="args">Optional format arguments</param>
        /// <returns>Localized string</returns>
        string GetString(string category, string key, string languageCode, string fallbackMessage = null, params object[] args);

        /// <summary>
        /// Sets the current culture for resource localization
        /// </summary>
        /// <param name="culture">The culture to set</param>
        void SetCulture(CultureInfo culture);

        /// <summary>
        /// Sets the current culture for resource localization using language code
        /// </summary>
        /// <param name="languageCode">Language code (e.g., "tr", "en")</param>
        void SetCulture(string languageCode);
    }
}
