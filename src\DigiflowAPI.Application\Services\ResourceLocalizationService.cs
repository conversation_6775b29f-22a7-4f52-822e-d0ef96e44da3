using DigiflowAPI.Application.Services.Interfaces;
using System.Globalization;
using System.Resources;
using System.Reflection;
using Microsoft.Extensions.Logging;

namespace DigiflowAPI.Application.Services
{
    public class ResourceLocalizationService : IResourceLocalizationService
    {
        private readonly ILogger<ResourceLocalizationService> _logger;
        private readonly Dictionary<string, ResourceManager> _resourceManagers;
        private CultureInfo _currentCulture;

        public ResourceLocalizationService(ILogger<ResourceLocalizationService> logger)
        {
            _logger = logger;
            _resourceManagers = new Dictionary<string, ResourceManager>();
            _currentCulture = CultureInfo.CurrentCulture;
            
            // Initialize resource managers for known workflow types
            InitializeResourceManagers();
        }

        public string GetString(string category, string key, CultureInfo culture = null, string fallbackMessage = null, params object[] args)
        {
            try
            {
                var targetCulture = culture ?? _currentCulture;
                var resourceManager = GetResourceManager(category);
                
                if (resourceManager == null)
                {
                    _logger.LogWarning("Resource manager not found for category: {Category}", category);
                    return FormatMessage(fallbackMessage ?? $"Resource category '{category}' not found", args);
                }

                var resourceValue = resourceManager.GetString(key, targetCulture);
                
                if (string.IsNullOrEmpty(resourceValue))
                {
                    _logger.LogWarning("Resource key '{Key}' not found in category '{Category}' for culture '{Culture}'", 
                        key, category, targetCulture.Name);
                    return FormatMessage(fallbackMessage ?? $"Key '{key}' not found in '{category}'", args);
                }

                return FormatMessage(resourceValue, args);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting localized string for category '{Category}', key '{Key}'", category, key);
                return FormatMessage(fallbackMessage ?? $"Error loading resource: {key}", args);
            }
        }

        public string GetString(string category, string key, string languageCode, string fallbackMessage = null, params object[] args)
        {
            var culture = GetCultureFromLanguageCode(languageCode);
            return GetString(category, key, culture, fallbackMessage, args);
        }

        public void SetCulture(CultureInfo culture)
        {
            _currentCulture = culture ?? CultureInfo.CurrentCulture;
        }

        public void SetCulture(string languageCode)
        {
            var culture = GetCultureFromLanguageCode(languageCode);
            SetCulture(culture);
        }

        private void InitializeResourceManagers()
        {
            try
            {
                // Get the Resources assembly
                var resourcesAssembly = Assembly.Load("DigiflowAPI.Resources");
                
                // Initialize resource managers for workflow types
                var workflowTypes = new[]
                {
                    "CreateWorkflow",
                    "ApprovalWorkflow",
                    "CancelWorkflow",
                    "FileUploadWorkflow",
                    "FinalizeWorkflow",
                    "ForwardWorkflow",
                    "RejectWorkflow",
                    "ResumeWorkflow",
                    "RollbackWorkflow",
                    "SendBackWorkflow",
                    "SendRequestToCommentWorkflow",
                    "SendToCommentWorkflow",
                    "SuspendWorkflow",
                    "Common"
                };

                foreach (var workflowType in workflowTypes)
                {
                    try
                    {
                        var resourceName = $"DigiflowAPI.Resources.Workflow.{workflowType}";
                        var resourceManager = new ResourceManager(resourceName, resourcesAssembly);
                        _resourceManagers[workflowType] = resourceManager;
                        
                        _logger.LogDebug("Initialized resource manager for {WorkflowType}", workflowType);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to initialize resource manager for {WorkflowType}", workflowType);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load DigiflowAPI.Resources assembly");
            }
        }

        private ResourceManager GetResourceManager(string category)
        {
            _resourceManagers.TryGetValue(category, out var resourceManager);
            return resourceManager;
        }

        private CultureInfo GetCultureFromLanguageCode(string languageCode)
        {
            if (string.IsNullOrWhiteSpace(languageCode))
                return CultureInfo.CurrentCulture;

            try
            {
                // Handle common language codes
                return languageCode.ToLowerInvariant() switch
                {
                    "tr" => new CultureInfo("tr-TR"),
                    "en" => new CultureInfo("en-US"),
                    "tr-tr" => new CultureInfo("tr-TR"),
                    "en-us" => new CultureInfo("en-US"),
                    _ => new CultureInfo(languageCode)
                };
            }
            catch (CultureNotFoundException ex)
            {
                _logger.LogWarning(ex, "Culture not found for language code: {LanguageCode}", languageCode);
                return CultureInfo.CurrentCulture;
            }
        }

        private string FormatMessage(string message, params object[] args)
        {
            if (string.IsNullOrEmpty(message) || args == null || args.Length == 0)
                return message;

            try
            {
                return string.Format(message, args);
            }
            catch (FormatException ex)
            {
                _logger.LogWarning(ex, "Failed to format message: {Message}", message);
                return message;
            }
        }
    }
}
