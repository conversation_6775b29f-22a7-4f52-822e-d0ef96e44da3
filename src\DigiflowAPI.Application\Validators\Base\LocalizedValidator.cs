using DigiflowAPI.Application.Services.Interfaces;
using FluentValidation;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace DigiflowAPI.Application.Validators.Base
{
    public abstract class LocalizedValidator<T> : AbstractValidator<T>
    {
        protected readonly IResourceLocalizationService _localizationService;
        protected readonly string _language;
        protected abstract string ResourceCategory { get; }

        protected LocalizedValidator(IHttpContextAccessor httpContextAccessor, IServiceProvider serviceProvider)
        {
            _localizationService = serviceProvider.GetRequiredService<IResourceLocalizationService>();
            
            // Get language from various sources in priority order
            _language = GetLanguageFromContext(httpContextAccessor);
            
            // Set the culture for the localization service
            _localizationService.SetCulture(_language);
        }

        /// <summary>
        /// Gets a localized message from resources
        /// </summary>
        /// <param name="key">Resource key</param>
        /// <param name="fallbackMessage">Fallback message if resource not found</param>
        /// <param name="args">Format arguments</param>
        /// <returns>Localized message</returns>
        protected string GetLocalizedMessage(string key, string fallbackMessage, params object[] args)
        {
            return _localizationService.GetString(ResourceCategory, key, _language, fallbackMessage, args);
        }

        /// <summary>
        /// Gets a localized message from resources using default fallback
        /// </summary>
        /// <param name="key">Resource key</param>
        /// <param name="args">Format arguments</param>
        /// <returns>Localized message</returns>
        protected string GetLocalizedMessage(string key, params object[] args)
        {
            return _localizationService.GetString(ResourceCategory, key, _language, $"Validation error: {key}", args);
        }

        private string GetLanguageFromContext(IHttpContextAccessor httpContextAccessor)
        {
            var httpContext = httpContextAccessor?.HttpContext;
            
            if (httpContext == null)
                return "tr"; // Default to Turkish

            // Priority 1: Check UserLanguage item (set by middleware)
            if (httpContext.Items.TryGetValue("UserLanguage", out var userLanguage) && 
                userLanguage is string userLang && !string.IsNullOrWhiteSpace(userLang))
            {
                return userLang;
            }

            // Priority 2: Check Accept-Language header
            var acceptLanguageHeader = httpContext.Request.Headers["Accept-Language"].FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(acceptLanguageHeader))
            {
                // Parse Accept-Language header and get the first language
                var languages = acceptLanguageHeader.Split(',')
                    .Select(l => l.Split(';')[0].Trim())
                    .Where(l => !string.IsNullOrWhiteSpace(l));

                var firstLanguage = languages.FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(firstLanguage))
                {
                    // Convert to simple language code
                    return firstLanguage.StartsWith("tr") ? "tr" : 
                           firstLanguage.StartsWith("en") ? "en" : "tr";
                }
            }

            // Priority 3: Check custom headers
            var languageHeader = httpContext.Request.Headers["X-Language"].FirstOrDefault() ??
                                httpContext.Request.Headers["Language"].FirstOrDefault();
            
            if (!string.IsNullOrWhiteSpace(languageHeader))
            {
                return languageHeader.ToLowerInvariant();
            }

            // Priority 4: Check query parameter
            var languageQuery = httpContext.Request.Query["lang"].FirstOrDefault() ??
                               httpContext.Request.Query["language"].FirstOrDefault();
            
            if (!string.IsNullOrWhiteSpace(languageQuery))
            {
                return languageQuery.ToLowerInvariant();
            }

            // Default fallback
            return "tr";
        }
    }
}
