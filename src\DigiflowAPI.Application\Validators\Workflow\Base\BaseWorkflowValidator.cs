using FluentValidation;
using Microsoft.AspNetCore.Http;
using DigiflowAPI.Application.Validators.Workflow.Configuration;
using DigiflowAPI.Application.Services.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System.Globalization;

namespace DigiflowAPI.Application.Validators.Workflow.Base
{
  /// <summary>
  /// Base abstract validator for workflow entities with dynamic rule configuration
  /// Eliminates repetitive validator code by providing common functionality
  /// </summary>
  /// <typeparam name="TEntity">The workflow entity type to validate</typeparam>
  public abstract class BaseWorkflowValidator<TEntity> : AbstractValidator<TEntity>
      where TEntity : class
  {
    protected readonly IHttpContextAccessor HttpContextAccessor;
    protected readonly ILogger Logger;
    protected readonly IResourceLocalizationService LocalizationService;
    protected readonly IServiceProvider ServiceProvider;
    protected readonly string Language;
    protected readonly DateTime Today;

    protected BaseWorkflowValidator(
        IHttpContextAccessor httpContextAccessor,
        ILogger logger,
        IServiceProvider serviceProvider)
    {
      HttpContextAccessor = httpContextAccessor;
      Logger = logger;
      ServiceProvider = serviceProvider;
      LocalizationService = serviceProvider.GetRequiredService<IResourceLocalizationService>();
      Language = GetLanguageFromContext(httpContextAccessor);
      Today = DateTime.Today;
      
      // Set the culture for the localization service
      LocalizationService.SetCulture(Language);
    }

    /// <summary>
    /// Gets the workflow configuration for the specific entity type
    /// </summary>
    protected abstract WorkflowValidationConfiguration GetWorkflowConfiguration();

    /// <summary>
    /// Gets the workflow action (Create, Approve, Cancel, etc.)
    /// </summary>
    protected abstract string GetWorkflowAction();

    /// <summary>
    /// Gets the current workflow state for approve actions
    /// </summary>
    protected virtual string GetWorkflowState(TEntity entity) => "InitialSchema";

    /// <summary>
    /// Gets the resource category for this workflow type
    /// </summary>
    protected virtual string GetResourceCategory()
    {
      var config = GetWorkflowConfiguration();
      return config?.WorkflowType ?? "Common";
    }

    /// <summary>
    /// Gets a localized validation message
    /// </summary>
    /// <param name="key">Resource key</param>
    /// <param name="fallbackMessage">Fallback message if resource not found</param>
    /// <param name="args">Format arguments</param>
    /// <returns>Localized message</returns>
    protected string GetLocalizedMessage(string key, string fallbackMessage, params object[] args)
    {
      try
      {
        // First try workflow-specific resources
        var message = LocalizationService.GetString(GetResourceCategory(), key, Language, null, args);
        
        // If not found, try Common resources
        if (string.IsNullOrEmpty(message) || message.Contains($"Key '{key}' not found"))
        {
          message = LocalizationService.GetString("Common", key, Language, fallbackMessage, args);
        }
        
        return message;
      }
      catch (Exception ex)
      {
        Logger.LogWarning(ex, "Error getting localized message for key: {Key}", key);
        return string.Format(fallbackMessage, args);
      }
    }

    /// <summary>
    /// Gets a localized validation message using default fallback
    /// </summary>
    /// <param name="key">Resource key</param>
    /// <param name="args">Format arguments</param>
    /// <returns>Localized message</returns>
    protected string GetLocalizedMessage(string key, params object[] args)
    {
      return GetLocalizedMessage(key, $"Validation error: {key}", args);
    }

    /// <summary>
    /// Gets language from HTTP context with priority order
    /// </summary>
    private string GetLanguageFromContext(IHttpContextAccessor httpContextAccessor)
    {
      var httpContext = httpContextAccessor?.HttpContext;
      
      if (httpContext == null)
        return "tr"; // Default to Turkish

      // Priority 1: Check UserLanguage item (set by middleware)
      if (httpContext.Items.TryGetValue("UserLanguage", out var userLanguage) && 
          userLanguage is string userLang && !string.IsNullOrWhiteSpace(userLang))
      {
        return userLang;
      }

      // Priority 2: Check Accept-Language header
      var acceptLanguageHeader = httpContext.Request.Headers["Accept-Language"].FirstOrDefault();
      if (!string.IsNullOrWhiteSpace(acceptLanguageHeader))
      {
        var languages = acceptLanguageHeader.Split(',')
          .Select(l => l.Split(';')[0].Trim())
          .Where(l => !string.IsNullOrWhiteSpace(l));

        var firstLanguage = languages.FirstOrDefault();
        if (!string.IsNullOrWhiteSpace(firstLanguage))
        {
          return firstLanguage.StartsWith("tr") ? "tr" : 
                 firstLanguage.StartsWith("en") ? "en" : "tr";
        }
      }

      // Priority 3: Check custom headers
      var languageHeader = httpContext.Request.Headers["X-Language"].FirstOrDefault() ??
                          httpContext.Request.Headers["Language"].FirstOrDefault();
      
      if (!string.IsNullOrWhiteSpace(languageHeader))
      {
        return languageHeader.ToLowerInvariant();
      }

      // Priority 4: Check query parameter
      var languageQuery = httpContext.Request.Query["lang"].FirstOrDefault() ??
                         httpContext.Request.Query["language"].FirstOrDefault();
      
      if (!string.IsNullOrWhiteSpace(languageQuery))
      {
        return languageQuery.ToLowerInvariant();
      }

      // Default fallback
      return "tr";
    }

    /// <summary>
    /// Gets the current workflow state for approve actions
    /// </summary>
    protected virtual string GetWorkflowState(TEntity entity) => "InitialSchema";

    /// <summary>
    /// Applies dynamic validation rules based on configuration
    /// </summary>
    protected void ApplyDynamicRules()
    {
      try
      {
        var config = GetWorkflowConfiguration();
        var action = GetWorkflowAction();

        Logger.LogDebug("Applying dynamic validation rules for action: {Action}, entity: {EntityType}",
            action, typeof(TEntity).Name);

        if (!config.ActionRules.ContainsKey(action))
        {
          Logger.LogWarning("No validation rules found for action: {Action}", action);
          return;
        }

        var actionRules = config.ActionRules[action];

        // Apply state-specific rules for approve actions
        if (action == "Approve" && actionRules.StateSpecificRules?.Count > 0)
        {
          ApplyStateSpecificRules(actionRules.StateSpecificRules);
        }
        else
        {
          // Apply general action rules
          ApplyGeneralActionRules(actionRules);
        }
      }
      catch (Exception ex)
      {
        Logger.LogError(ex, "Error applying dynamic validation rules for {EntityType}", typeof(TEntity).Name);
        throw;
      }
    }

    /// <summary>
    /// Applies state-specific validation rules for approve actions
    /// </summary>
    private void ApplyStateSpecificRules(Dictionary<string, ValidationRuleSet> stateRules)
    {
      // For state-specific rules, we need to determine state at validation time
      RuleFor(entity => entity)
          .Custom((entity, context) =>
          {
            var currentState = GetWorkflowState(entity);
            Logger.LogDebug("Validating for state: {State}", currentState);

            if (stateRules.ContainsKey(currentState))
            {
              var rules = stateRules[currentState];
              ValidateAgainstRuleSet(entity, context, rules);
            }
            else if (stateRules.ContainsKey("Default"))
            {
              var rules = stateRules["Default"];
              ValidateAgainstRuleSet(entity, context, rules);
            }
          });
    }

    /// <summary>
    /// Applies general action validation rules
    /// </summary>
    private void ApplyGeneralActionRules(ActionValidationRules actionRules)
    {
      if (actionRules.RequiredFields?.Count > 0)
      {
        ApplyRequiredFieldRules(actionRules.RequiredFields);
      }

      if (actionRules.DateRules?.Count > 0)
      {
        ApplyDateRules(actionRules.DateRules);
      }

      if (actionRules.NumericRules?.Count > 0)
      {
        ApplyNumericRules(actionRules.NumericRules);
      }

      if (actionRules.CustomRules?.Count > 0)
      {
        ApplyCustomRules(actionRules.CustomRules);
      }
    }

    /// <summary>
    /// Validates entity against a specific rule set
    /// </summary>
    private void ValidateAgainstRuleSet(TEntity entity, ValidationContext<TEntity> context, ValidationRuleSet rules)
    {
      if (rules.RequiredFields?.Count > 0)
      {
        ValidateRequiredFields(entity, context, rules.RequiredFields);
      }

      if (rules.DateRules?.Count > 0)
      {
        ValidateDateFields(entity, context, rules.DateRules);
      }

      if (rules.NumericRules?.Count > 0)
      {
        ValidateNumericFields(entity, context, rules.NumericRules);
      }

      if (rules.CustomRules?.Count > 0)
      {
        ValidateCustomFields(entity, context, rules.CustomRules);
      }
    }

    /// <summary>
    /// Applies required field validation rules using reflection
    /// </summary>
    private void ApplyRequiredFieldRules(List<RequiredFieldRule> requiredFields)
    {
      foreach (var rule in requiredFields)
      {
        var property = typeof(TEntity).GetProperty(rule.FieldName);
        if (property != null)
        {
          // Create a dynamic rule for this property
          RuleFor(entity => entity).Custom((entity, context) =>
              {
                var value = property.GetValue(entity);
                if (value == null || IsValueEmpty(value))
                {
                  var message = GetLocalizedMessage(rule.ErrorKey, rule.DefaultMessage);
                  context.AddFailure(rule.FieldName, message);
                }
              });
        }
      }
    }

    /// <summary>
    /// Applies date validation rules
    /// </summary>
    private void ApplyDateRules(List<DateRule> dateRules)
    {
      foreach (var rule in dateRules)
      {
        var property = typeof(TEntity).GetProperty(rule.FieldName);
        if (property != null && (property.PropertyType == typeof(DateTime) ||
            property.PropertyType == typeof(DateTime?)))
        {
          RuleFor(entity => entity)
              .Custom((entity, context) =>
              {
                var value = property.GetValue(entity) as DateTime?;
                if (ValidateDateRule(entity, value, rule, out string errorMessage))
                {
                  context.AddFailure(rule.FieldName, errorMessage);
                }
              });
        }
      }
    }

    /// <summary>
    /// Applies numeric validation rules
    /// </summary>
    private void ApplyNumericRules(List<NumericRule> numericRules)
    {
      foreach (var rule in numericRules)
      {
        var property = typeof(TEntity).GetProperty(rule.FieldName);
        if (property != null && IsNumericType(property.PropertyType))
        {
          RuleFor(entity => entity).Custom((entity, context) =>
              {
                var value = property.GetValue(entity);
                if (value != null && ValidateNumericRule(value, rule, out string errorMessage))
                {
                  context.AddFailure(rule.FieldName, errorMessage);
                }
              });
        }
      }
    }

    /// <summary>
    /// Applies custom validation rules
    /// </summary>
    private void ApplyCustomRules(List<CustomRule> customRules)
    {
      foreach (var rule in customRules)
      {
        RuleFor(entity => entity)
            .Custom((entity, context) =>
            {
              if (!ValidateCustomRule(entity, rule, out string errorMessage))
              {
                context.AddFailure(rule.TargetField ?? "General", errorMessage);
              }
            });
      }
    }        /// <summary>
             /// Validates required fields using reflection
             /// </summary>
    private void ValidateRequiredFields(TEntity entity, ValidationContext<TEntity> context, List<RequiredFieldRule> requiredFields)
    {
      foreach (var rule in requiredFields)
      {
        var property = typeof(TEntity).GetProperty(rule.FieldName);
        if (property != null)
        {
          var value = property.GetValue(entity);
          if (value == null || IsValueEmpty(value))
          {
            var message = GetLocalizedMessage(rule.ErrorKey, rule.DefaultMessage);
            context.AddFailure(rule.FieldName, message);
          }
        }
      }
    }

    /// <summary>
    /// Validates date fields
    /// </summary>
    private void ValidateDateFields(TEntity entity, ValidationContext<TEntity> context, List<DateRule> dateRules)
    {
      foreach (var rule in dateRules)
      {
        var property = typeof(TEntity).GetProperty(rule.FieldName);
        if (property != null)
        {
          var value = property.GetValue(entity) as DateTime?;
          if (ValidateDateRule(entity, value, rule, out string errorMessage))
          {
            context.AddFailure(rule.FieldName, errorMessage);
          }
        }
      }
    }        /// <summary>
             /// Validates numeric fields
             /// </summary>
    private void ValidateNumericFields(TEntity entity, ValidationContext<TEntity> context, List<NumericRule> numericRules)
    {
      foreach (var rule in numericRules)
      {
        var property = typeof(TEntity).GetProperty(rule.FieldName);
        if (property != null)
        {
          var value = property.GetValue(entity);
          if (value != null && ValidateNumericRule(value, rule, out string errorMessage))
          {
            context.AddFailure(rule.FieldName, errorMessage);
          }
        }
      }
    }

    /// <summary>
    /// Validates custom fields
    /// </summary>
    private void ValidateCustomFields(TEntity entity, ValidationContext<TEntity> context, List<CustomRule> customRules)
    {
      foreach (var rule in customRules)
      {
        if (!ValidateCustomRule(entity, rule, out string errorMessage))
        {
          context.AddFailure(rule.TargetField ?? "General", errorMessage);
        }
      }
    }        /// <summary>
             /// Validates a specific date rule
             /// </summary>
    private bool ValidateDateRule(TEntity entity, DateTime? value, DateRule rule, out string errorMessage)
    {
      errorMessage = string.Empty;

      if (rule.Required && value == null)
      {
        errorMessage = GetLocalizedMessage(rule.ErrorKey, rule.DefaultMessage);
        return true;
      }

      if (value.HasValue)
      {
        switch (rule.ComparisonType)
        {
          case "MinDate":
            if (value < (rule.CompareToDate ?? Today))
            {
              errorMessage = GetLocalizedMessage(rule.ErrorKey, rule.DefaultMessage);
              return true;
            }
            break;

          case "MaxDate":
            if (value > (rule.CompareToDate ?? Today))
            {
              errorMessage = GetLocalizedMessage(rule.ErrorKey, rule.DefaultMessage);
              return true;
            }
            break;

          case "CompareToField":
            if (!string.IsNullOrEmpty(rule.CompareToField))
            {
              var compareProperty = typeof(TEntity).GetProperty(rule.CompareToField);
              if (compareProperty != null)
              {
                var compareValue = compareProperty.GetValue(entity) as DateTime?;
                if (compareValue.HasValue && value < compareValue)
                {
                  errorMessage = GetLocalizedMessage(rule.ErrorKey, rule.DefaultMessage);
                  return true;
                }
              }
            }
            break;
        }
      }

      return false;
    }        /// <summary>
             /// Validates a specific numeric rule
             /// </summary>
    private bool ValidateNumericRule(object value, NumericRule rule, out string errorMessage)
    {
      errorMessage = string.Empty;

      if (rule.Required && (value == null || Convert.ToDecimal(value) == 0))
      {
        errorMessage = GetLocalizedMessage(rule.ErrorKey, rule.DefaultMessage);
        return true;
      }

      if (value != null)
      {
        var numericValue = Convert.ToDecimal(value);

        if (rule.MinValue.HasValue && numericValue < rule.MinValue)
        {
          errorMessage = GetLocalizedMessage(rule.ErrorKey, rule.DefaultMessage);
          return true;
        }

        if (rule.MaxValue.HasValue && numericValue > rule.MaxValue)
        {
          errorMessage = GetLocalizedMessage(rule.ErrorKey, rule.DefaultMessage);
          return true;
        }
      }

      return false;
    }

    /// <summary>
    /// Validates a custom rule
    /// </summary>
    private bool ValidateCustomRule(TEntity entity, CustomRule rule, out string errorMessage)
    {
      errorMessage = GetLocalizedMessage(rule.ErrorKey, rule.DefaultMessage);

      // Implement custom validation logic based on rule type
      switch (rule.RuleType)
      {
        case "SelfDelegationCheck":
          return ValidateSelfDelegation(entity, rule);

        case "ConditionalRequired":
          return ValidateConditionalRequired(entity, rule);

        case "FileUploadRequired":
          return ValidateFileUpload(entity, rule);

        default:
          Logger.LogWarning("Unknown custom rule type: {RuleType}", rule.RuleType);
          return true;
      }
    }

    /// <summary>
    /// Validates self-delegation prevention
    /// </summary>
    private bool ValidateSelfDelegation(TEntity entity, CustomRule rule)
    {
      var userIdProperty = typeof(TEntity).GetProperty("DelegatedLoginId");
      var currentUserProperty = typeof(TEntity).GetProperty("CreatedBy");

      if (userIdProperty != null && currentUserProperty != null)
      {
        var delegatedUserId = userIdProperty.GetValue(entity);
        var currentUserId = currentUserProperty.GetValue(entity);

        return delegatedUserId?.Equals(currentUserId) == true;
      }

      return false;
    }

    /// <summary>
    /// Validates conditional required fields
    /// </summary>
    private bool ValidateConditionalRequired(TEntity entity, CustomRule rule)
    {
      if (string.IsNullOrEmpty(rule.ConditionField) || string.IsNullOrEmpty(rule.TargetField))
        return false;

      var conditionProperty = typeof(TEntity).GetProperty(rule.ConditionField);
      var targetProperty = typeof(TEntity).GetProperty(rule.TargetField);

      if (conditionProperty != null && targetProperty != null)
      {
        var conditionValue = conditionProperty.GetValue(entity);
        var targetValue = targetProperty.GetValue(entity);

        // If condition is met and target field is empty, validation fails
        if (conditionValue != null && targetValue != null &&
            IsConditionMet(conditionValue, rule.ConditionValue) && IsValueEmpty(targetValue))
        {
          return false;
        }
      }

      return true;
    }

    /// <summary>
    /// Validates file upload requirements
    /// </summary>
    private bool ValidateFileUpload(TEntity entity, CustomRule rule)
    {
      var fileProperty = typeof(TEntity).GetProperty(rule.TargetField);
      if (fileProperty != null)
      {
        var fileValue = fileProperty.GetValue(entity);
        return fileValue != null && !IsValueEmpty(fileValue);
      }

      return true;
    }        /// <summary>
             /// Checks if a condition is met
             /// </summary>
    private bool IsConditionMet(object? conditionValue, string? expectedValue)
    {
      if (conditionValue == null && expectedValue == null) return true;
      if (conditionValue == null || expectedValue == null) return false;

      return conditionValue.ToString()?.Equals(expectedValue, StringComparison.OrdinalIgnoreCase) == true;
    }

    /// <summary>
    /// Checks if a value is considered empty
    /// </summary>
    private bool IsValueEmpty(object value)
    {
      return value == null ||
             string.IsNullOrWhiteSpace(value.ToString()) ||
             value.ToString() == "0";
    }

    /// <summary>
    /// Checks if a type is numeric
    /// </summary>
    private bool IsNumericType(Type type)
    {
      var numericTypes = new[]
      {
                typeof(int), typeof(int?), typeof(long), typeof(long?),
                typeof(decimal), typeof(decimal?), typeof(double), typeof(double?),
                typeof(float), typeof(float?)
            };

      return numericTypes.Contains(type);
    }
  }
}
