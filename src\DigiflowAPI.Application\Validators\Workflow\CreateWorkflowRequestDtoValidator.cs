using DigiflowAPI.Application.Resources;
using DigiflowAPI.Application.Validators.Base;
using FluentValidation;
using Microsoft.AspNetCore.Http;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using DigiflowAPI.Application.Validators.Workflow.Configurations;
using DigiflowAPI.Application.DTOs.Workflow.Operation;

namespace DigiflowAPI.Domain.Entities.Workflow
{
    public class CreateWorkflowRequestDtoValidator : LocalizedValidator<CreateWorkflowRequestDto>
    {
        protected override string ResourceCategory => "CreateWorkflow";
        private readonly IServiceProvider _serviceProvider;

        public CreateWorkflowRequestDtoValidator(
            IHttpContextAccessor httpContextAccessor,
            IServiceProvider serviceProvider) : base(httpContextAccessor, serviceProvider)
        {
            _serviceProvider = serviceProvider;

            RuleFor(x => x.WorkflowName)
                .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredWorkflowName", "Workflow name is required."));

            RuleFor(x => x.EntityJson)
                .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredEntityJson", "Entity JSON is required.")).Custom((entityJson, context) =>
                {
                    var workflowName = context.InstanceToValidate.WorkflowName;

                    try
                    {
                        var jsonDocument = JsonDocument.Parse(entityJson.GetRawText());
                        var rootElement = jsonDocument.RootElement;

                        // Use dynamic validation system instead of hardcoded validators
                        // For now, we'll validate the JSON structure is valid and defer
                        // detailed validation to the actual workflow handlers
                        var configurationProvider = _serviceProvider.GetRequiredService<IWorkflowValidationConfigurationProvider>();
                        var config = configurationProvider.GetConfiguration(workflowName);

                        if (config == null)
                        {
                            context.AddFailure("WorkflowName", GetLocalizedMessage("invalidWorkflowType", "Invalid workflow type: {0}", workflowName));
                        }

                        // The JSON is valid if we reach here, detailed validation will happen in handlers
                    }
                    catch (JsonException ex)
                    {
                        context.AddFailure("EntityJson", GetLocalizedMessage("jsonParseError", "Error parsing JSON: {0}", ex.Message));
                    }
                    catch (Exception ex)
                    {
                        context.AddFailure("EntityJson", GetLocalizedMessage("unexpectedError", "Unexpected error: {0}", ex.Message));
                    }
                });
        }
    }
}