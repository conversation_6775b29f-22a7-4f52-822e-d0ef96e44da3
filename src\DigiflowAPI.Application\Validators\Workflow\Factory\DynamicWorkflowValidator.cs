using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DigiflowAPI.Application.Validators.Workflow.Base;
using DigiflowAPI.Application.Validators.Workflow.Configuration;
using DigiflowAPI.Application.Validators.Workflow.Configurations;
using DigiflowAPI.Application.Validators.Workflow.Factory;

namespace DigiflowAPI.Application.Validators.Workflow.Factory
{
  /// <summary>
  /// Dynamic workflow validator that applies validation rules based on configuration
  /// This eliminates the need for multiple specific validator classes
  /// </summary>
  /// <typeparam name="T">The entity type to validate</typeparam>
  public class DynamicWorkflowValidator<T> : BaseWorkflowValidator<T> where T : class
  {
    private readonly string _workflowType;
    private readonly string _action;
    private readonly string? _state;
    private readonly IWorkflowValidationConfigurationProvider _configurationProvider;

    public DynamicWorkflowValidator(
        IServiceProvider serviceProvider,
        string workflowType,
        string action,
        string? state = null)
        : base(
            serviceProvider.GetRequiredService<IHttpContextAccessor>(),
            serviceProvider.GetRequiredService<ILogger<DynamicWorkflowValidator<T>>>(),
            serviceProvider)
    {
      _workflowType = workflowType;
      _action = action;
      _state = state;
      _configurationProvider = serviceProvider.GetRequiredService<IWorkflowValidationConfigurationProvider>();

      // Apply dynamic rules during construction
      ApplyDynamicRules();
    }

    protected override WorkflowValidationConfiguration GetWorkflowConfiguration()
    {
      return _configurationProvider.GetConfiguration(_workflowType);
    }

    protected override string GetWorkflowAction()
    {
      return _action;
    }

    protected override string GetResourceCategory()
    {
      // Use workflow type as resource category (e.g., "Contract", "Delegation", "BiFikrimVar")
      // This should match the resource file names in DigiflowAPI.Resources/Workflow/
      return _workflowType;
    }

    protected override string GetWorkflowState(T entity)
    {
      // If state is provided explicitly, use it
      if (!string.IsNullOrEmpty(_state))
      {
        return _state;
      }

      // Try to determine state from entity properties
      return DetermineStateFromEntity(entity) ?? "InitialSchema";
    }

    /// <summary>
    /// Determines the workflow state from the entity properties
    /// </summary>
    private string? DetermineStateFromEntity(T entity)
    {
      // Look for common state properties
      var stateProperty = typeof(T).GetProperty("WorkflowState") ??
                         typeof(T).GetProperty("State") ??
                         typeof(T).GetProperty("CurrentState");

      if (stateProperty != null)
      {
        var stateValue = stateProperty.GetValue(entity);
        return stateValue?.ToString();
      }

      return null;
    }
  }
}
