using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.Validators.Base;
using FluentValidation;
using Microsoft.AspNetCore.Http;

namespace DigiflowAPI.Domain.Entities.Workflow
{
    public class RejectWorkflowRequestDtoValidator : LocalizedValidator<RejectWorkflowRequestDto>
    {
        protected override string ResourceCategory => "RejectWorkflow";

        public RejectWorkflowRequestDtoValidator(
            IHttpContextAccessor httpContextAccessor,
            IServiceProvider serviceProvider) : base(httpContextAccessor, serviceProvider)
        {
            RuleFor(x => x.WorkflowName)
                .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredWorkflowName", "Workflow name is required"));

            RuleFor(x => x.InstanceId)
                .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredInstanceId", "Instance ID is required"))
                .GreaterThan(0).WithMessage(_ => GetLocalizedMessage("invalidInstanceId", "Instance ID must be greater than 0"));

            RuleFor(x => x.LoginUserId)
                .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredLoginUserId", "Login User ID is required"))
                .GreaterThan(0).WithMessage(_ => GetLocalizedMessage("invalidLoginUserId", "Login User ID must be greater than 0"));

            RuleFor(x => x.Comment)
                .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredComment", "Comment is required for rejection"))
                .MaximumLength(1000).WithMessage(_ => GetLocalizedMessage("commentTooLong", "Comment cannot exceed 1000 characters"));
        }
    }
}
