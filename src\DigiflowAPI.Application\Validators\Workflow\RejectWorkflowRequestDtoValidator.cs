﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.Validators.Base;
using FluentValidation;
using Microsoft.AspNetCore.Http;

namespace DigiflowAPI.Domain.Entities.Workflow
{
    /// <summary>
    /// Validator for RejectWorkflowRequestDto
    /// Note: Workflow-specific validation rules (like RejectReason requirements) are handled by the configuration-based system
    /// This validator only handles basic structural validation
    /// </summary>
    public class RejectWorkflowRequestDtoValidator : LocalizedValidator<RejectWorkflowRequestDto>
    {
        protected override string ResourceCategory => "Common"; // Using Common resources for general validations

        public RejectWorkflowRequestDtoValidator(
            IHttpContextAccessor httpContextAccessor,
            IServiceProvider serviceProvider) : base(httpContextAccessor, serviceProvider)
        {
            // Only validate the basic structure
            // Workflow-specific rules (like RejectReason) are handled by DynamicWorkflowValidator based on configuration
            
            RuleFor(x => x.WorkflowName)
                .NotEmpty().WithMessage(_ => GetLocalizedMessage("requiredWorkflowName", "Workflow name is required"));

            RuleFor(x => x.InstanceId)
                .GreaterThan(0).WithMessage(_ => GetLocalizedMessage("invalidInstanceId", "Instance ID must be greater than 0"));

            RuleFor(x => x.LoginUserId)
                .GreaterThan(0).WithMessage(_ => GetLocalizedMessage("invalidLoginUserId", "Login User ID must be greater than 0"));

            // Comment validation is intentionally minimal here
            // Workflow-specific comment requirements are handled by configuration rules
        }
    }
}
