using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using DigiflowMobileAPI.Interfaces;
using DigiflowMobileAPI.Services;
using DigiflowMobileAPI.Models.Auth;
using DigiflowMobileAPI.Models.Configuration;
using DigiflowMobileAPI.Middleware;
using DigiflowAPI.Security.Authentication.Services;
using Serilog;
using Serilog.Events;
using System.Reflection;
using System.Runtime.Loader;
using System.Xml;
using Path = System.IO.Path;

// Create a bootstrap logger for startup issues
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .WriteTo.Console()
    .CreateBootstrapLogger();

// Test basic logging capability first
TestStartup.TestLogging();

try
{
    // Ensure log directory exists - try multiple locations
    var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
    var currentDirectory = Directory.GetCurrentDirectory();

    string logDirectory;
    try
    {
        // Try project directory first
        logDirectory = Path.Combine(currentDirectory, "Logs");
        Directory.CreateDirectory(logDirectory);
        Log.Information("Log directory created at: {LogDirectory}", logDirectory);
    }
    catch (Exception ex)
    {
        Log.Warning(ex, "Failed to create logs in current directory: {CurrentDirectory}", currentDirectory);
        try
        {
            // Try base directory
            logDirectory = Path.Combine(baseDirectory, "Logs");
            Directory.CreateDirectory(logDirectory);
            Log.Information("Log directory created at base directory: {LogDirectory}", logDirectory);
        }
        catch (Exception ex2)
        {
            Log.Error(ex2, "Failed to create logs in base directory: {BaseDirectory}", baseDirectory);
            // Use temp directory as last resort
            logDirectory = Path.Combine(Path.GetTempPath(), "DigiflowMobileAPI", "Logs");
            Directory.CreateDirectory(logDirectory);
            Log.Information("Using temp directory for logs: {LogDirectory}", logDirectory);
        }
    }

    // Configure Serilog with file logging
    Log.Logger = new LoggerConfiguration()
        .MinimumLevel.Debug()
        .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
        .MinimumLevel.Override("System", LogEventLevel.Warning)
        .Enrich.FromLogContext()
        .Enrich.WithMachineName()
        .Enrich.WithEnvironmentName()
        .Enrich.WithThreadId()
        .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
        .WriteTo.File(
            Path.Combine(logDirectory, "digiflow-mobile-startup-.txt"),
            rollingInterval: RollingInterval.Day,
            retainedFileCountLimit: 30,
            fileSizeLimitBytes: 50_000_000,
            rollOnFileSizeLimit: true,
            outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] [{MachineName}] [{ThreadId}] {Message:lj}{NewLine}{Exception}")
        .WriteTo.File(
            Path.Combine(logDirectory, "digiflow-mobile-errors-.txt"),
            restrictedToMinimumLevel: LogEventLevel.Error,
            rollingInterval: RollingInterval.Day,
            retainedFileCountLimit: 60,
            fileSizeLimitBytes: 50_000_000,
            rollOnFileSizeLimit: true,
            outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] [{MachineName}] [{ThreadId}] {Message:lj}{NewLine}{Exception}{NewLine}---{NewLine}")
        .CreateLogger();
}
catch (Exception ex)
{
    // If all logging setup fails, try basic console logging
    Console.WriteLine($"CRITICAL: Failed to set up logging: {ex}");
    Log.Logger = new LoggerConfiguration()
        .MinimumLevel.Debug()
        .WriteTo.Console()
        .CreateLogger();
}

try
{
    Log.Information("================================");
    Log.Information("Starting DigiflowAPI.MobileApi");
    Log.Information("Base Directory: {BaseDirectory}", AppDomain.CurrentDomain.BaseDirectory);
    Log.Information("Current Directory: {CurrentDirectory}", Directory.GetCurrentDirectory());
    Log.Information("Machine: {MachineName}", Environment.MachineName);
    Log.Information("User: {UserName}", Environment.UserName);
    Log.Information("CLR Version: {Version}", Environment.Version);
    Log.Information("OS Version: {OSVersion}", Environment.OSVersion);
    Log.Information("Process ID: {ProcessId}", Environment.ProcessId);
    Log.Information("================================");

    #region Assembly Resolution
    AssemblyLoadContext.Default.Resolving += (context, assemblyName) =>
    {
        try
        {
            Log.Debug("Resolving assembly: {AssemblyName}", assemblyName);

            // Only try to load specific assemblies from deployment path
            // Don't load Serilog or other NuGet packages from there
            if (assemblyName.Name != null &&
                !assemblyName.Name.StartsWith("Serilog") &&
                !assemblyName.Name.StartsWith("Microsoft.") &&
                !assemblyName.Name.StartsWith("System.") &&
                !assemblyName.Name.StartsWith("Newtonsoft.") &&
                !assemblyName.Name.StartsWith("AutoMapper"))
            {
                var assemblyPath = Path.Combine(@"\\dtl1iis3\Deployment", $"{assemblyName.Name}.dll");
                if (File.Exists(assemblyPath))
                {
                    Log.Information("Loading assembly from: {AssemblyPath}", assemblyPath);
                    return context.LoadFromAssemblyPath(assemblyPath);
                }
            }

            Log.Debug("Assembly will be resolved by default loader: {AssemblyName}", assemblyName);
            return null;
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error resolving assembly: {AssemblyName}", assemblyName);
            return null; // Don't throw, just return null and let the app continue
        }
    };

    // Oracle.DataAccess will be loaded on demand if needed
    #endregion

    Log.Information("Creating WebApplication builder");
    var builder = WebApplication.CreateBuilder(args);

    // Add Serilog to the builder - use explicit configuration to avoid assembly loading issues
    builder.Host.UseSerilog((context, services, configuration) =>
    {
        var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
        Directory.CreateDirectory(logDirectory);

        configuration
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.Hosting.Lifetime", LogEventLevel.Information)
            .MinimumLevel.Override("Microsoft.AspNetCore.Authentication", LogEventLevel.Debug)
            .MinimumLevel.Override("Microsoft.AspNetCore.Authorization", LogEventLevel.Debug)
            .MinimumLevel.Override("DigiflowMobileAPI", LogEventLevel.Debug)
            .MinimumLevel.Override("DigiflowAPI", LogEventLevel.Debug)
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning)
            .MinimumLevel.Override("System", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .Enrich.WithMachineName()
            .Enrich.WithThreadId()
            .Enrich.WithEnvironmentName()
            .WriteTo.Console()
            .WriteTo.File(
                Path.Combine(logDirectory, "digiflow-mobile-api-.txt"),
                rollingInterval: RollingInterval.Day,
                restrictedToMinimumLevel: LogEventLevel.Debug,
                retainedFileCountLimit: 30,
                outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}")
            .WriteTo.File(
                Path.Combine(logDirectory, "digiflow-mobile-requests-.txt"),
                rollingInterval: RollingInterval.Day,
                restrictedToMinimumLevel: LogEventLevel.Debug,
                retainedFileCountLimit: 7,
                outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] MobileAPI: {Message:lj}{NewLine}{Exception}")
            .WriteTo.File(
                Path.Combine(logDirectory, "digiflow-mobile-errors-.txt"),
                restrictedToMinimumLevel: LogEventLevel.Error,
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 60,
                outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] ERROR: {Message:lj}{NewLine}{Exception}{NewLine}---{NewLine}");
    });

    Log.Information("Loading connection strings from app.config");
    #region ConnectionStrings from app.config
    try
    {
        var appConfigPath = "./app.config";
        if (!File.Exists(appConfigPath))
        {
            appConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "app.config");
        }

        Log.Information("Loading app.config from: {AppConfigPath}", appConfigPath);

        if (File.Exists(appConfigPath))
        {
            var xmlDoc = new XmlDocument();
            xmlDoc.Load(appConfigPath);
            var connectionStrings = xmlDoc.SelectSingleNode("//connectionStrings");

            if (connectionStrings != null)
            {
                var connectionCount = 0;
                foreach (XmlNode node in connectionStrings.ChildNodes)
                {
                    if (node.Attributes != null)
                    {
                        var name = node.Attributes["name"]?.InnerText;
                        var connectionString = node.Attributes["connectionString"]?.InnerText;

                        if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(connectionString))
                        {
                            builder.Configuration.AddInMemoryCollection(new[]
                            {
                                new KeyValuePair<string, string>($"ConnectionStrings:{name}", connectionString)
                            });
                            connectionCount++;
                            Log.Debug("Added connection string: {Name}", name);
                        }

                        var providerName = node.Attributes["providerName"]?.InnerText;
                        if (!string.IsNullOrEmpty(providerName))
                        {
                            builder.Configuration.AddInMemoryCollection(new[]
                            {
                                new KeyValuePair<string, string>($"ConnectionStrings:{name}_ProviderName", providerName)
                            });
                        }
                    }
                }
                Log.Information("Loaded {ConnectionCount} connection strings from app.config", connectionCount);
            }
            else
            {
                Log.Warning("No connectionStrings section found in app.config");
            }
        }
        else
        {
            Log.Warning("app.config file not found at: {AppConfigPath}", appConfigPath);
        }
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Error loading connection strings from app.config");
        throw;
    }
    #endregion

    Log.Information("Registering services");

    // ─── Configuration ──────────────────────────────────────────────────────────────
    var jwtIssuer = builder.Configuration["Jwt:Issuer"] ?? "DigiflowAPI";
    var jwtAudience = builder.Configuration["Jwt:Audience"] ?? "DigiflowAPI";
    var jwtSecret = builder.Configuration["Jwt:Secret"] ?? "3KvxnOU+fW0HNCsuv2SHRjIqBn+rN15pf7EomQPkQ1M=";
    var jwtExpiryMinutes = int.Parse(builder.Configuration["Jwt:ExpiryMinutes"] ?? "129600");

    // ─── Services ───────────────────────────────────────────────────────────────────
    builder.Services.AddControllers(options =>
    {
        // Add route conventions if needed
    });
    builder.Services.AddEndpointsApiExplorer();

    // Configure Swagger
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new() { Title = "DigiHR Mobile API", Version = "v1" });

        // Define JWT Bearer auth security scheme
        c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
        {
            Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            Name = "Authorization",
            In = Microsoft.OpenApi.Models.ParameterLocation.Header,
            Type = Microsoft.OpenApi.Models.SecuritySchemeType.Http,
            Scheme = "Bearer",
            BearerFormat = "JWT"
        });

        // Apply security requirements globally
        c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
        {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
        });

        // Custom schema IDs to avoid conflicts
        c.CustomSchemaIds(type => type.FullName?.Replace("+", "."));

        // Ignore obsolete endpoints
        c.IgnoreObsoleteActions();
        c.IgnoreObsoleteProperties();
    });

    // Configure authentication
    builder.Services.AddAuthentication(options =>
    {
        options.DefaultScheme = "Bearer";
        options.DefaultChallengeScheme = "Bearer";
        options.DefaultAuthenticateScheme = "Bearer";
    })
    .AddJwtBearer("Bearer", options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSecret)),
            ValidateIssuer = true,
            ValidIssuer = jwtIssuer,
            ValidateAudience = true,
            ValidAudience = jwtAudience,
            ValidateLifetime = true,
            ClockSkew = TimeSpan.FromMinutes(5)
        };

        // Don't throw exceptions, let the middleware handle them
        options.Events = new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                // Log authentication failures but don't stop the request pipeline
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                logger.LogWarning("Authentication failed: {Message}", context.Exception.Message);
                return Task.CompletedTask;
            }
        };
    });

    builder.Services.AddAuthorization(options =>
    {
        // Don't use a fallback policy - let endpoints decide
        // options.FallbackPolicy = options.DefaultPolicy;
    });

    builder.Services.AddHttpContextAccessor();

    // Register custom services
    builder.Services.Configure<JwtOptions>(builder.Configuration.GetSection("Jwt"));
    builder.Services.Configure<DigiflowApiOptions>(builder.Configuration.GetSection(DigiflowApiOptions.SectionName));
    builder.Services.AddSingleton<WindowsAuthService>();
    builder.Services.AddScoped<IAuthService, AuthService>();
    builder.Services.AddScoped<DigiflowAPI.MobileAPI.Interfaces.IDigiflowService, DigiflowAPI.MobileAPI.Services.DigiflowService>();
    builder.Services.AddScoped<DigiflowAPI.MobileAPI.Interfaces.IFrameworkService, DigiflowAPI.MobileAPI.Services.FrameworkService>();
    
    // Register localization service
    builder.Services.AddScoped<DigiflowAPI.Application.Services.Interfaces.IResourceLocalizationService, DigiflowAPI.Application.Services.ResourceLocalizationService>();

    // Add HttpClient for HomeService with environment-specific configuration
    builder.Services.AddHttpClient<IHomeService, HomeService>((serviceProvider, client) =>
    {
        var configuration = serviceProvider.GetRequiredService<IConfiguration>();
        var environment = serviceProvider.GetRequiredService<IWebHostEnvironment>();

        // Get the base URL from configuration based on environment
        string? baseUrl = configuration["DigiflowApi:BaseUrl"];

        if (string.IsNullOrEmpty(baseUrl))
        {
            if (environment.IsDevelopment())
            {
                baseUrl = "http://digiflowtest.digiturk.com.tr/api";
            }
            else if (environment.IsEnvironment("Test"))
            {
                baseUrl = "https://digiflowtest.digiturk.com.tr/api";
            }
            else
            {
                baseUrl = "http://digiflow.digiturk.com.tr/api";
            }
        }

        client.BaseAddress = new Uri(baseUrl);
        client.DefaultRequestHeaders.Add("User-Agent", "DigiflowMobileApi");
    })
    .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
    {
        UseDefaultCredentials = true,
        AllowAutoRedirect = true,
        AutomaticDecompression = System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate
    });

    // Configure CORS with specific allowed origins
    builder.Services.AddCors(options =>
    {
        options.AddDefaultPolicy(
            policy =>
            {
                // Define allowed origins from configuration
                var allowedOrigins = builder.Configuration.GetSection("AllowedOrigins").Get<string[]>() ??
                    new[] {
                    "http://localhost:3000",
                    "http://localhost:5173",
                    "http://digiflowtest.digiturk.com.tr",
                    "https://digiflowtest.digiturk.com.tr",
                    "http://digiflow.digiturk.com.tr",
                    "https://digiflow.digiturk.com.tr"
                    };

                policy.WithOrigins(allowedOrigins)
                      .AllowAnyHeader()
                      .AllowAnyMethod();
            });

        // Keep a specific policy for endpoints that need credentials
        options.AddPolicy("AllowWithCredentials",
            policyBuilder => policyBuilder
                .WithOrigins(
                    builder.Configuration.GetSection("AllowedOrigins").Get<string[]>() ??
                    new[] {
                    "http://localhost:3000",
                    "http://localhost:5173",
                    "http://digiflowtest.digiturk.com.tr",
                    "https://digiflowtest.digiturk.com.tr",
                    "http://digiflow.digiturk.com.tr",
                    "https://digiflow.digiturk.com.tr"
                    })
                .AllowAnyMethod()
                .AllowAnyHeader()
                .AllowCredentials());
    });

    // Add HTTP client for calling DigiflowAPI if needed
    builder.Services.AddHttpClient("DigiflowApi", client =>
    {
        // Get the base URL from configuration based on environment
        string? baseUrl = null;

        // First try to get the direct BaseUrl setting
        baseUrl = builder.Configuration["DigiflowApi:BaseUrl"];

        // If not found, use environment-specific setting
        if (string.IsNullOrEmpty(baseUrl))
        {
            if (builder.Environment.IsDevelopment())
            {
                baseUrl = builder.Configuration["DigiflowApi:Development"];
            }
            else
            {
                baseUrl = builder.Configuration["DigiflowApi:Production"];
            }
        }

        // Fallback to HTTP for internal server communication
        baseUrl ??= "https://digiflowtest.digiturk.com.tr/api";

        client.BaseAddress = new Uri(baseUrl);
        client.DefaultRequestHeaders.Add("User-Agent", "MobileApi-Proxy");

        // Log the selected base URL
        Console.WriteLine($"DigiflowApi BaseUrl: {baseUrl}");
    })
    .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
    {
        UseDefaultCredentials = true,
        AllowAutoRedirect = true,
        AutomaticDecompression = System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate
    });

    var app = builder.Build();

    // Add global security headers for Mobile API
    app.Use(async (context, next) =>
    {
        var headers = context.Response.Headers;

        // Security headers (more restrictive for mobile)
        headers["X-XSS-Protection"] = "1; mode=block";
        headers["X-Content-Type-Options"] = "nosniff";
        headers["X-Frame-Options"] = "DENY";  // More restrictive for mobile
        headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains; preload";
        headers["Referrer-Policy"] = "no-referrer";  // More private for mobile
        headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=(), payment=(), usb=()";

        // CSP for Mobile API (more restrictive)
        headers["Content-Security-Policy"] = "default-src 'self'; script-src 'self'; style-src 'self'; img-src 'self' data:;";

        await next();
    });

    // Configure the HTTP request pipeline
    if (app.Environment.IsDevelopment())
    {
        app.UseDeveloperExceptionPage();
    }

    // Enable Swagger for all environments (can be restricted later if needed)
    app.UseSwagger(c =>
    {
        //c.SerializeAsV2 = false;
    });
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "DigiHR Mobile API v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
        c.DefaultModelsExpandDepth(-1); // Hide models section by default
        c.DisplayRequestDuration();
        c.EnableDeepLinking();
        c.EnableFilter();
        c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
    });

    // Apply CORS policy before authentication
    app.UseCors();

    // Add Serilog request logging early in the pipeline
    app.UseSerilogRequestLogging(options =>
    {
        options.MessageTemplate = "HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms";
        options.GetLevel = (httpContext, elapsed, ex) =>
        {
            if (ex != null || httpContext.Response.StatusCode >= 500)
                return LogEventLevel.Error;
            if (httpContext.Response.StatusCode >= 400)
                return LogEventLevel.Warning;
            if (elapsed > 3000) // Log slow requests as warnings
                return LogEventLevel.Warning;
            return LogEventLevel.Information;
        };
        options.EnrichDiagnosticContext = (diagnosticContext, httpContext) =>
        {
            diagnosticContext.Set("RequestHost", httpContext.Request.Host.Value);
            diagnosticContext.Set("RequestScheme", httpContext.Request.Scheme);
            diagnosticContext.Set("UserAgent", httpContext.Request.Headers["User-Agent"].ToString());
            diagnosticContext.Set("RemoteIP", httpContext.Connection.RemoteIpAddress?.ToString());
            diagnosticContext.Set("UserName", httpContext.User?.Identity?.Name ?? "Anonymous");
            diagnosticContext.Set("RequestId", httpContext.TraceIdentifier);
            diagnosticContext.Set("IsMobile", httpContext.Request.Headers.ContainsKey("X-From-Mobile-WebView") ||
                                            httpContext.Request.Headers.ContainsKey("X-Mobile-App"));
        };
    });

    // Add detailed request/response logging for debugging mobile issues
    app.UseMiddleware<DigiflowMobileAPI.Middleware.RequestResponseLoggingMiddleware>();

    // Diagnostic middleware for request logging
    app.Use(async (context, next) =>
    {
        var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
        logger.LogInformation("Request started: {Method} {Path}, ContentType: {ContentType}",
            context.Request.Method,
            context.Request.Path,
            context.Request.ContentType);

        // Check specifically for login paths to ensure they are accessible
        var path = context.Request.Path.Value?.ToLower() ?? "";
        if (path.EndsWith("/auth/login") || path.EndsWith("/mobile/auth/login"))
        {
            logger.LogInformation("Login endpoint access: {Path}", path);
        }

        await next();

        logger.LogInformation("Response completed: {StatusCode} for {Method} {Path}",
            context.Response.StatusCode,
            context.Request.Method,
            context.Request.Path);
    });

    // Add middleware in correct order for authentication flow
    app.UseMiddleware<JwtAuthenticationMiddleware>();
    app.UseAuthentication();
    app.UseAuthorization();

    // Add controllers with explicit routes - make sure public endpoints are accessible
    app.MapControllers().AllowAnonymous();

    Log.Information("Starting application");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
    throw;
}
finally
{
    Log.CloseAndFlush();
}