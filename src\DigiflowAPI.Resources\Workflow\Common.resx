﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="WorkflowAdminMessage" xml:space="preserve">
    <value>Administrator of related workflow (XXX).
You can forward all your questions concerning the workflow to workflow administrator.</value>
  </data>
  <data name="WorkflowAdminMessageWarning" xml:space="preserve">
    <value>An error was encountered while fetching the Admin List.</value>
  </data>
  <data name="RequiredPropertiesMissing" xml:space="preserve">
    <value>Required properties are missing from the request</value>
  </data>
  
  <!-- Common Workflow Validation Messages (English) -->
  
  <!-- Required Field Validations -->
  <data name="requiredWorkflowName" xml:space="preserve">
    <value>Workflow name is required</value>
  </data>
  <data name="requiredInstanceId" xml:space="preserve">
    <value>Instance ID is required</value>
  </data>
  <data name="requiredLoginUserId" xml:space="preserve">
    <value>Login User ID is required</value>
  </data>
  <data name="requiredComment" xml:space="preserve">
    <value>Comment is required</value>
  </data>
  <data name="requiredEntityJson" xml:space="preserve">
    <value>Entity JSON is required</value>
  </data>
  
  <!-- Action-Specific Required Fields -->
  <data name="rejectReasonRequired" xml:space="preserve">
    <value>Reject reason is required</value>
  </data>
  <data name="forwardLoginIdRequired" xml:space="preserve">
    <value>Forward login ID is required</value>
  </data>
  <data name="sendToCommentLoginIdRequired" xml:space="preserve">
    <value>Send to comment login ID is required</value>
  </data>
  <data name="cancelReasonRequired" xml:space="preserve">
    <value>Cancel reason is required</value>
  </data>
  <data name="rollbackReasonRequired" xml:space="preserve">
    <value>Rollback reason is required</value>
  </data>
  <data name="suspendCommentRequired" xml:space="preserve">
    <value>Suspend comment is required</value>
  </data>
  <data name="finalNoteRequired" xml:space="preserve">
    <value>Final note is required</value>
  </data>
  <data name="fileRequired" xml:space="preserve">
    <value>File is required</value>
  </data>
  
  <!-- Invalid Value Validations -->
  <data name="invalidWorkflowInstanceId" xml:space="preserve">
    <value>Invalid workflow instance ID</value>
  </data>
  <data name="invalidInstanceId" xml:space="preserve">
    <value>Instance ID must be greater than 0</value>
  </data>
  <data name="invalidLoginUserId" xml:space="preserve">
    <value>Login User ID must be greater than 0</value>
  </data>
  <data name="invalidWorkflowType" xml:space="preserve">
    <value>Invalid workflow type: {0}</value>
  </data>
  
  <!-- Date Validations -->
  <data name="requiredStartDate" xml:space="preserve">
    <value>Start date is required</value>
  </data>
  <data name="requiredFinishDate" xml:space="preserve">
    <value>Finish date is required</value>
  </data>
  <data name="requiredSuspendUntil" xml:space="preserve">
    <value>Suspend until date is required</value>
  </data>
  <data name="startDateMustBeLater" xml:space="preserve">
    <value>Start date must be later than today</value>
  </data>
  <data name="finishDateMustBeLater" xml:space="preserve">
    <value>Finish date must be later than start date</value>
  </data>
  <data name="suspendUntilMustBeLater" xml:space="preserve">
    <value>Suspend until date must be later than today</value>
  </data>
  <data name="invalidDateFormat" xml:space="preserve">
    <value>Invalid date format</value>
  </data>
  <data name="startDateInvalid" xml:space="preserve">
    <value>Invalid start date</value>
  </data>
  <data name="finishDateInvalid" xml:space="preserve">
    <value>Invalid finish date</value>
  </data>
  <data name="suspendUntilInvalid" xml:space="preserve">
    <value>Invalid suspend until date</value>
  </data>
  <data name="suspendDateValidation" xml:space="preserve">
    <value>Suspend date must be in the future</value>
  </data>
  
  <!-- Length Validations -->
  <data name="commentTooLong" xml:space="preserve">
    <value>Comment cannot exceed {0} characters</value>
  </data>
  <data name="workflowNameTooLong" xml:space="preserve">
    <value>Workflow name cannot exceed {0} characters</value>
  </data>
  
  <!-- JSON Validations -->
  <data name="jsonParseError" xml:space="preserve">
    <value>Error parsing JSON: {0}</value>
  </data>
  <data name="invalidEntityJson" xml:space="preserve">
    <value>Invalid Entity JSON format</value>
  </data>
  
  <!-- General Errors -->
  <data name="unexpectedError" xml:space="preserve">
    <value>Unexpected error: {0}</value>
  </data>
  <data name="validationError" xml:space="preserve">
    <value>Validation error: {0}</value>
  </data>
  
  <!-- User/Permission Validations -->
  <data name="requiredUser" xml:space="preserve">
    <value>User selection is required</value>
  </data>
  <data name="atLeastOneUser" xml:space="preserve">
    <value>At least one user must be selected</value>
  </data>
  <data name="atLeastOneWorkflow" xml:space="preserve">
    <value>At least one workflow must be selected</value>
  </data>
  <data name="selfDelegationError" xml:space="preserve">
    <value>You cannot delegate workflows to yourself</value>
  </data>
  <data name="ownDelegation" xml:space="preserve">
    <value>You cannot delegate your own workflows</value>
  </data>
  <data name="workflowNotFound" xml:space="preserve">
    <value>Workflow not found</value>
  </data>
</root>