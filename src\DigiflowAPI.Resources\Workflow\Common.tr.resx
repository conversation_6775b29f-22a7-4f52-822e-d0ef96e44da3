﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="WorkflowAdminMessage" xml:space="preserve">
    <value>İlgili akışın, akış yöneticisi (XXX).
Akışla ilgili her türlü soru ve talebinizi akış yöneticilerine iletebilirsiniz.</value>
  </data>
  <data name="WorkflowAdminMessageWarning" xml:space="preserve">
    <value>Admin Listesi getirilirken bir hata ile karşılaşıldı.</value>
  </data>
  <data name="RequiredPropertiesMissing" xml:space="preserve">
    <value>Bazı zorunlu alanlar eksik</value>
  </data>
  
  <!-- Common Workflow Validation Messages (Turkish) -->
  
  <!-- Required Field Validations -->
  <data name="requiredWorkflowName" xml:space="preserve">
    <value>Akış adı zorunludur</value>
  </data>
  <data name="requiredInstanceId" xml:space="preserve">
    <value>Talep ID zorunludur</value>
  </data>
  <data name="requiredLoginUserId" xml:space="preserve">
    <value>Kullanıcı ID zorunludur</value>
  </data>
  <data name="requiredComment" xml:space="preserve">
    <value>Yorum zorunludur</value>
  </data>
  <data name="requiredEntityJson" xml:space="preserve">
    <value>Varlık JSON'u zorunludur</value>
  </data>
  
  <!-- Action-Specific Required Fields -->
  <data name="rejectReasonRequired" xml:space="preserve">
    <value>Red nedeni zorunludur</value>
  </data>
  <data name="forwardLoginIdRequired" xml:space="preserve">
    <value>Yönlendirme kullanıcı ID'si zorunludur</value>
  </data>
  <data name="sendToCommentLoginIdRequired" xml:space="preserve">
    <value>Yoruma gönderilecek kullanıcı ID'si zorunludur</value>
  </data>
  <data name="cancelReasonRequired" xml:space="preserve">
    <value>İptal nedeni zorunludur</value>
  </data>
  <data name="rollbackReasonRequired" xml:space="preserve">
    <value>Geri alma nedeni zorunludur</value>
  </data>
  <data name="suspendCommentRequired" xml:space="preserve">
    <value>Askıya alma açıklaması zorunludur</value>
  </data>
  <data name="finalNoteRequired" xml:space="preserve">
    <value>Final notu zorunludur</value>
  </data>
  <data name="fileRequired" xml:space="preserve">
    <value>Dosya zorunludur</value>
  </data>
  
  <!-- Invalid Value Validations -->
  <data name="invalidWorkflowInstanceId" xml:space="preserve">
    <value>Geçersiz iş akışı örnek ID'si</value>
  </data>
  <data name="invalidInstanceId" xml:space="preserve">
    <value>Talep ID 0'dan büyük olmalıdır</value>
  </data>
  <data name="invalidLoginUserId" xml:space="preserve">
    <value>Kullanıcı ID 0'dan büyük olmalıdır</value>
  </data>
  <data name="invalidWorkflowType" xml:space="preserve">
    <value>Geçersiz akış türü: {0}</value>
  </data>
  
  <!-- Date Validations -->
  <data name="requiredStartDate" xml:space="preserve">
    <value>Başlangıç tarihi zorunludur</value>
  </data>
  <data name="requiredFinishDate" xml:space="preserve">
    <value>Bitiş tarihi zorunludur</value>
  </data>
  <data name="requiredSuspendUntil" xml:space="preserve">
    <value>Askıya alma bitiş tarihi zorunludur</value>
  </data>
  <data name="startDateMustBeLater" xml:space="preserve">
    <value>Başlangıç tarihi bugünden sonra olmalıdır</value>
  </data>
  <data name="finishDateMustBeLater" xml:space="preserve">
    <value>Bitiş tarihi başlangıç tarihinden sonra olmalıdır</value>
  </data>
  <data name="suspendUntilMustBeLater" xml:space="preserve">
    <value>Askıya alma bitiş tarihi bugünden sonra olmalıdır</value>
  </data>
  <data name="invalidDateFormat" xml:space="preserve">
    <value>Geçersiz tarih formatı</value>
  </data>
  <data name="startDateInvalid" xml:space="preserve">
    <value>Geçersiz başlangıç tarihi</value>
  </data>
  <data name="finishDateInvalid" xml:space="preserve">
    <value>Geçersiz bitiş tarihi</value>
  </data>
  <data name="suspendUntilInvalid" xml:space="preserve">
    <value>Geçersiz askıya alma bitiş tarihi</value>
  </data>
  <data name="suspendDateValidation" xml:space="preserve">
    <value>Askıya alma tarihi gelecekte olmalıdır</value>
  </data>
  
  <!-- Length Validations -->
  <data name="commentTooLong" xml:space="preserve">
    <value>Yorum {0} karakteri geçemez</value>
  </data>
  <data name="workflowNameTooLong" xml:space="preserve">
    <value>Akış adı {0} karakteri geçemez</value>
  </data>
  
  <!-- JSON Validations -->
  <data name="jsonParseError" xml:space="preserve">
    <value>JSON ayrıştırma hatası: {0}</value>
  </data>
  <data name="invalidEntityJson" xml:space="preserve">
    <value>Geçersiz Varlık JSON formatı</value>
  </data>
  
  <!-- General Errors -->
  <data name="unexpectedError" xml:space="preserve">
    <value>Beklenmeyen hata: {0}</value>
  </data>
  <data name="validationError" xml:space="preserve">
    <value>Doğrulama hatası: {0}</value>
  </data>
  
  <!-- User/Permission Validations -->
  <data name="requiredUser" xml:space="preserve">
    <value>Kullanıcı seçimi zorunludur</value>
  </data>
  <data name="atLeastOneUser" xml:space="preserve">
    <value>En az bir kullanıcı seçilmelidir</value>
  </data>
  <data name="atLeastOneWorkflow" xml:space="preserve">
    <value>En az bir iş akışı seçilmelidir</value>
  </data>
  <data name="selfDelegationError" xml:space="preserve">
    <value>Kendi kendinize akış delege edemezsiniz</value>
  </data>
  <data name="ownDelegation" xml:space="preserve">
    <value>Kendi akışlarınızı delege edemezsiniz</value>
  </data>
  <data name="workflowNotFound" xml:space="preserve">
    <value>Akış bulunamadı</value>
  </data>
</root>