﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="WorkflowCreatedSuccessfully" xml:space="preserve">
    <value>İş akışı başarıyla oluşturuldu</value>
  </data>
  <data name="checkForDelegationError" xml:space="preserve">
    <value>Seçmiş olduğunuz akış ilgili Tarihlerde Farklı bir kullanıcıya delege edilmiştir</value>
  </data>
  <data name="activeDelegateError" xml:space="preserve">
    <value>İlgili Tarihlerde Delegesi Olduğunuz kişiye akışlarınızı delege edemezsiniz</value>
  </data>
  <data name="ownDelegation" xml:space="preserve">
    <value>Kendinize akış delege edemezsiniz</value>
  </data>
  <data name="delegationDelegate" xml:space="preserve">
    <value>Delegasyon akışı delege edilemez</value>
  </data>
  <data name="requiredStartDate" xml:space="preserve">
    <value>Başlangıç tarihi gereklidir</value>
  </data>
  <data name="startDateMustBeLater" xml:space="preserve">
    <value>Başlangıç tarihi bugünden sonra olmalıdır</value>
  </data>
  <data name="startDateInvalid" xml:space="preserve">
    <value>Başlangıç tarihi geçerli bir tarih olmalıdır</value>
  </data>
  <data name="requiredFinishDate" xml:space="preserve">
    <value>Bitiş tarihi gereklidir</value>
  </data>
  <data name="finishDateMustBeLater" xml:space="preserve">
    <value>Delegasyon Bitiş Tarihi Delegasyon Başlangıç Tarihinden büyük olmalıdır</value>
  </data>
  <data name="finishDateInvalid" xml:space="preserve">
    <value>Bitiş tarihi geçerli bir tarih olmalıdır</value>
  </data>
  <data name="atLeastOneUser" xml:space="preserve">
    <value>En az bir kullanıcı seçilmelidir</value>
  </data>
  <data name="requiredUser" xml:space="preserve">
    <value>Kullanıcı seçimi zorunludur</value>
  </data>
  <data name="atLeastOneWorkflow" xml:space="preserve">
    <value>En az bir iş akışı seçilmelidir</value>
  </data>
  <data name="requiredEntityJson" xml:space="preserve">
    <value>EntityJson gereklidir</value>
  </data>
  <data name="invalidEntityJson" xml:space="preserve">
    <value>Geçersiz EntityJson formatı</value>
  </data>
  <data name="flowDefIdListRequired" xml:space="preserve">
    <value>En az 1 adet akış seçilmelidir</value>
  </data>
  <data name="flowDefIdRequired" xml:space="preserve">
    <value>Akış ID bulunamadı</value>
  </data>
  <data name="flowInstanceIdRequired" xml:space="preserve">
    <value>Talep ID bulunamadı</value>
  </data>
  <data name="ownMonitoringRequest" xml:space="preserve">
    <value>Kendi akışlarınız için görüntüleme talebinde bulunamazsınız</value>
  </data>
  <data name="ownerLoginIdNotFound" xml:space="preserve">
    <value>Lütfen bir kullanıcı seçiniz</value>
  </data>
  <data name="ownStartedMonitoringRequest" xml:space="preserve">
    <value>Kendi başlattığınız bir akış için görüntüleme talebinde bulunamazsınız</value>
  </data>
  <data name="workflowNotFound" xml:space="preserve">
    <value>Akış bulunamadı</value>
  </data>
  <data name="requiredWorkflowName" xml:space="preserve">
    <value>Akış adı zorunludur</value>
  </data>
  <data name="invalidWorkflowType" xml:space="preserve">
    <value>Geçersiz akış türü: {0}</value>
  </data>
  <data name="jsonParseError" xml:space="preserve">
    <value>JSON ayrıştırma hatası: {0}</value>
  </data>
  <data name="unexpectedError" xml:space="preserve">
    <value>Beklenmeyen hata: {0}</value>
  </data>
</root>