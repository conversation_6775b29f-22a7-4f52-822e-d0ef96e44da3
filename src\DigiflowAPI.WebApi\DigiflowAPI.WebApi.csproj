<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>false</InvariantGlobalization>
  </PropertyGroup>
<PropertyGroup>
  <AssemblyLoadTracing>true</AssemblyLoadTracing>
</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="HotChocolate.AspNetCore" Version="15.1.6" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Core" Version="5.3.0" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Owin" Version="5.3.0" />
    <PackageReference Include="Microsoft.AspNet.WebApi.OwinSelfHost" Version="5.3.0" />
    <PackageReference Include="Microsoft.AspNet.WebApi.WebHost" Version="5.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Microsoft.Owin.Host.SystemWeb" Version="4.2.2" />
    <PackageReference Include="Microsoft.Windows.Compatibility" Version="9.0.6" />
    <PackageReference Include="NHibernate.ByteCode.LinFu" Version="1.1.0" />
    <PackageReference Include="Oracle.ManagedDataAccess.Core" Version="23.8.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="9.0.1" />
    <PackageReference Include="System.Formats.Asn1" Version="9.0.6" />
    <PackageReference Include="System.Numerics.Vectors" Version="4.6.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DigiflowAPI.Application\DigiflowAPI.Application.csproj" />
    <ProjectReference Include="..\DigiflowAPI.Domain\DigiflowAPI.Domain.csproj" />
    <ProjectReference Include="..\DigiflowAPI.Infrastructure\DigiflowAPI.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="DevExpress.Data.v12.1">
      <HintPath>\\dtl1iis3\Deployment\DevExpress.Data.v12.1.dll</HintPath>
    </Reference>
    <Reference Include="KurumsalRazorClassLibrary">
      <HintPath>..\..\..\..\..\..\..\..\TFS\KurumsalRazorClassLibrary.publish\KurumsalRazorClassLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess">
      <HintPath>..\..\..\..\..\..\..\..\oracle_files\WINDOWS.X64_193000_client_home\ODP.NET\bin\4\Oracle.DataAccess.dll</HintPath>
    </Reference>

    <Reference Include="System.Data.Entity">
      <HintPath>\\dtl1iis3\Deployment\System.Data.Entity.dll</HintPath>
    </Reference>

    <Reference Include="System.EnterpriseServices">
      <HintPath>C:\TFS\System.EnterpriseServices.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
