﻿using DigiflowAPI.Application;
using MediatR;
using System.Reflection;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Globalization;
using Microsoft.AspNetCore.Localization;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Infrastructure.Services;
using DigiflowAPI.Infrastructure.Services;
using DigiflowAPI.Infrastructure.Helpers;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Infrastructure.Data.Repositories;
using DigiflowAPI.Application.Interfaces.Configuration;
using DigiflowAPI.Infrastructure.Configuration;
using FluentValidation;
using DigiflowAPI.Application.Queries.Users;
using DigiflowAPI.Infrastructure.Security.Authorization.Services.Workflow.Abstract;
using DigiflowAPI.Infrastructure.Security.Authorization.Services.Workflow.Concrete;
using DigiflowAPI.Application.Mappers;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Application.Handlers.Workflow;
using DigiflowAPI.Infrastructure.Services.SharePoint;
using DigiflowAPI.Application.Interfaces.Services.Workflow;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Models;
using DigiflowAPI.Application.Commands.Workflow;
using DigiflowAPI.Application.Queries.Organizations;
using DigiflowAPI.Application.Handlers.Users;
using DigiflowAPI.Application.Validators.User;
using DigiflowAPI.Application.Validators.Organization;
using DigiflowAPI.Infrustructure.DataAccess;
using DigiflowAPI.Application.Commands.Organization;
using DigiflowAPI.Api.Extensions;
using Microsoft.AspNetCore.Mvc;

namespace DigiflowAPI.WebApi.Extensions
{
    public static class ServiceExtensions
    {
        public static IServiceCollection AddApiServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Add Application layer services
            services.AddApplication();

            services.AddMemoryCache();

            //HTTP Service
            services.AddHttpClient<IHttpService, HttpService>()
                .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
                {
                    UseDefaultCredentials = true
                });

            // Register services and repositories
            services.AddScoped<IWorkflowHelperService, WorkflowHelperService>();
            services.AddScoped<IWorkflowRepository, WorkflowRepository>();
            services.AddScoped<IGenericMailService, GenericMailService>();
            services.AddScoped<WorkflowTypeHelper>();
            services.AddSingleton<WorkflowTypeInitializer>();

            // Use Func<T> for services with circular dependencies
            services.AddScoped<Func<IWorkflowHelperService>>(sp => () => sp.GetRequiredService<IWorkflowHelperService>());
            services.AddScoped<Func<IWorkflowRepository>>(sp => () => sp.GetRequiredService<IWorkflowRepository>());
            services.AddScoped<Func<IGenericMailService>>(sp => () => sp.GetRequiredService<IGenericMailService>());
            services.AddScoped<Func<WorkflowTypeHelper>>(sp => () => sp.GetRequiredService<WorkflowTypeHelper>());

            // Register other services
            services.AddScoped<ISharePointService, SharePointService>();
            services.AddScoped<ILogicalGroupService, LogicalGroupService>();
            services.AddScoped<IOrganizationService, OrganizationService>();
            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<ITabVisibilityService, TabVisibilityService>();
            services.AddScoped<IActionPermissionService, ActionPermissionService>(); services.AddScoped<ILogServices, LogServices>();
            
            // Register localization service
            services.AddScoped<DigiflowAPI.Application.Services.Interfaces.IResourceLocalizationService, DigiflowAPI.Application.Services.ResourceLocalizationService>();

            services.AddLogging();
            services.AddTransient<IRequestHandler<UpdateEntityCommand, bool>, UpdateEntityCommandHandler>();




            services.AddSingleton<ISharePointConfiguration, SharePointConfiguration>();
            services.AddSingleton<IWebServicesConfiguration, WebServicesConfiguration>();
            services.AddScoped<IWorkflowTypeHelper, WorkflowTypeHelper>();


            services.Configure<PermissionProcessSettings>(configuration.GetSection("ServiceSettings"));
            services.AddScoped<IPermissionProcessService, PermissionProcessService>();


            //Helper Services
            services.AddTransient<IGlobalHelpers, GlobalHelpers>();

            //Session, HTTPContext and Cache Services
            services.AddHttpContextAccessor();
            services.AddDistributedMemoryCache();
            services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromMinutes(30);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
            });

            //Authentication Service
            // DELETE ↓ (Windows only – now registered globally in Program.cs)
            // services.AddAuthentication(IISDefaults.AuthenticationScheme); // ← remove, policy scheme now handles both

            // Register MediatR and scan for handlers
            services.AddMediatR(cfg =>
            {
                cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
                cfg.RegisterServicesFromAssembly(typeof(Program).Assembly);
                cfg.RegisterServicesFromAssembly(typeof(ApplicationEntryPoint).Assembly);
                cfg.RegisterServicesFromAssembly(typeof(GetAllWorkflowHandler).Assembly);
                cfg.RegisterServicesFromAssembly(typeof(GetAllAdminWorkflowHandler).Assembly);
                cfg.RegisterServicesFromAssembly(typeof(GetWorkflowQueryHandler<>).Assembly); // Register open generic handlers
            });            // Register open generic handlers
            services.AddOpenGenericHandlerType(typeof(Program).Assembly);            // Auto Mapper Services
            services.AddAutoMapper(typeof(MappingProfile).Assembly);

            // Oracle services (must be registered before workflow services)
            //services.AddODAR();
            services.AddSingleton<IOracleConnectionManager, OracleConnectionManager>();
            services.AddSingleton<IOracleDataAccessRepositoryFactory, OracleDataAccessRepositoryFactory>();


            // Fluent Validation Services
            services.AddTransient<IValidator<GetDepartmentsQuery>, GetDepartmentQueryValidator>();
            services.AddTransient<IValidator<GetSchemaCommand>, GetSchemaQueryValidator>();
            services.AddTransient<IValidator<GetUsersByDepartmentIdQuery>, GetUsersByDepartmentIdQueryValidator>();

            //Custom Authorization Services
            services.AddScoped<IWorkflowAuthorizationService, WorkflowAuthorizationService>();
            services.AddSingleton<WorkflowAuthorizationServiceFactory>();

            //Cors Service
            services.AddCors(options =>
            {
                options.AddPolicy("AllowSpecificOrigin", policy =>
                {
                    // Get React app origins from configuration to match appsettings.json
                    var reactOrigins = configuration.GetSection("ReactAppOrigins").Get<string[]>() ?? [];

                    // Only allow specifically configured origins
                    policy.WithOrigins(reactOrigins)
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .AllowCredentials()
                        // Add these specifically for SignalR
                        .WithExposedHeaders("X-Login-Id", "X-Workflow-Instance-Id", "X-Workflow-Definition-Id",
                            "X-Is-SysAdmin", "X-Workflow-Copy-Instance-Id", "X-Workflow-Authorization");
                });
            });


            //Localization Service
            var supportedCultures = new[] { "tr-TR", "en-US" };
            services.Configure<RequestLocalizationOptions>(options =>
            {
                options.DefaultRequestCulture = new RequestCulture("en-US");
                options.SupportedCultures = supportedCultures.Select(c => new CultureInfo(c)).ToList();
                options.SupportedUICultures = supportedCultures.Select(c => new CultureInfo(c)).ToList();
            });

            //Swagger Service
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "Digiflow API", Version = "v1" });
                options.CustomOperationIds(apiDesc =>
                {
                    return apiDesc.TryGetMethodInfo(out MethodInfo methodInfo) ? methodInfo.Name : null;
                });

                // Add explicit type mappings for file uploads to prevent parameter generation errors
                options.MapType<IFormFile>(() => new OpenApiSchema
                {
                    Type = "string",
                    Format = "binary"
                });
                options.MapType<IFormFileCollection>(() => new OpenApiSchema
                {
                    Type = "array",
                    Items = new OpenApiSchema
                    {
                        Type = "string",
                        Format = "binary"
                    }
                });

                options.OperationFilter<AddRequiredHeaderParameter>();
                options.OperationFilter<FileUploadOperationFilter>();
            });


            // Add clean workflow services (replaces massive repetitive registrations)
            services.AddCleanWorkflowServices();

            return services;
        }

        public static void AddOpenGenericHandlerType(this IServiceCollection services, Assembly assembly)
        {
            var types = assembly.GetTypes();
            foreach (var type in types)
            {
                var interfaces = type.GetInterfaces()
                    .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IRequestHandler<,>));

                foreach (var handlerInterface in interfaces)
                {
                    services.AddTransient(handlerInterface, type);
                }
            }
        }

        public class AddRequiredHeaderParameter : IOperationFilter
        {
            public void Apply(OpenApiOperation operation, OperationFilterContext context)
            {
                operation.Parameters ??= [];

                operation.Parameters.Add(new OpenApiParameter
                {
                    Name = "X-Login-ID",
                    In = ParameterLocation.Header,
                    Required = false,
                    Schema = new OpenApiSchema
                    {
                        Type = "integer"
                    }
                });
            }
        }

        public class FileUploadOperationFilter : IOperationFilter
        {
            public void Apply(OpenApiOperation operation, OperationFilterContext context)
            {
                // Check if this is a file upload operation by looking at the action method
                var methodInfo = context.MethodInfo;
                var hasFormFile = methodInfo.GetParameters()
                    .Any(p => p.ParameterType == typeof(IFormFile) || p.ParameterType == typeof(IFormFileCollection));

                if (!hasFormFile)
                    return;

                // Clear any existing parameters that might cause issues
                operation.Parameters?.Clear();

                // Manually create the multipart/form-data request body
                operation.RequestBody = new OpenApiRequestBody
                {
                    Required = true,
                    Content = new Dictionary<string, OpenApiMediaType>
                    {
                        ["multipart/form-data"] = new OpenApiMediaType
                        {
                            Schema = new OpenApiSchema
                            {
                                Type = "object",
                                Properties = new Dictionary<string, OpenApiSchema>()
                            }
                        }
                    }
                };

                var schema = operation.RequestBody.Content["multipart/form-data"].Schema;

                // Add properties for each parameter in the method
                foreach (var parameter in methodInfo.GetParameters())
                {
                    if (parameter.ParameterType == typeof(IFormFile))
                    {
                        schema.Properties[parameter.Name ?? "file"] = new OpenApiSchema
                        {
                            Type = "string",
                            Format = "binary",
                            Description = $"Upload file for {parameter.Name}"
                        };
                    }
                    else if (parameter.ParameterType == typeof(IFormFileCollection))
                    {
                        schema.Properties[parameter.Name ?? "files"] = new OpenApiSchema
                        {
                            Type = "array",
                            Items = new OpenApiSchema
                            {
                                Type = "string",
                                Format = "binary"
                            },
                            Description = $"Upload multiple files for {parameter.Name}"
                        };
                    }
                    else if (parameter.GetCustomAttributes(typeof(FromFormAttribute), false).Any())
                    {
                        // Handle other [FromForm] parameters
                        schema.Properties[parameter.Name ?? "value"] = new OpenApiSchema
                        {
                            Type = "string",
                            Description = $"Form field for {parameter.Name}"
                        };
                    }
                }
            }
        }

    }

}
