{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Jwt": {"Secret": "3KvxnOU+fW0HNCsuv2SHRjIqBn+rN15pf7EomQPkQ1M=", "Key": "3KvxnOU+fW0HNCsuv2SHRjIqBn+rN15pf7EomQPkQ1M=", "Issuer": "DigiflowAPI", "Audience": "DigiflowAPI", "AccessTokenMinutes": 129600, "ExpiryMinutes": 129600}, "Ldap": {"Path": "LDAP://dtldap.digiturk.local"}, "ReactAppOrigins": ["http://localhost:3000", "http://localhost:5173", "http://digiflowtest.digiturk.com.tr", "https://digiflowtest.digiturk.com.tr", "http://digiflowtest", "https://digiflowtest", "https://digiflow.digiturk.com.tr", "http://digiflow.digiturk.com.tr", "https://digiflow", "http://digiflow"], "SYSTEM_ADMIN_USERS": "DTKBAYRAKTAR;DTZKUCUK;DIGIFLOW_SA;SPSMOSS_SA;DTBGUNAY;DTUMKORKMAZ;DTMKASAPOGLU;DTYAELMAS", "DOTNET_SYSTEM_GLOBALIZATION_INVARIANT": "0", "AppSettings": {"AdminGrupUser": "DTKBAYRAKTAR,DTZKUCUK,DIGIFLOW_SA,SPSMOSS_SA,DTBGUNAY,DTMKASAPOGLU,DTUMKORKMAZ,DTYAELMAS", "ApplicationName": "", "aspnet:MaxHttpCollectionKeys": "100000", "AssemblyLookupFolders": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\bin", "AuthenticateUserOnAnyServiceCall": "true", "AvansEndPointDefaul": "http://dtl1sp1:20000/Service.asmx", "AydinlatmaMetniServisi": "http://sdp-lcl-tst.digiturk.net/Maintenance_Test/virtual/basic/PersonalDataProtectionBS.svc?wsdl", "DBS_REQUEST_ROW_SIZE": "100", "DBSLIVE_INQUIRY": "MARACANA", "DBSLIVE_INQUIRY_APPSTR": "Digiflow", "DBSLIVE_INQUIRY_UNIQUE": "4!fedL0w", "DBSLIVE_RAPOR_APPSTR": "Rota", "DBSLIVE_RAPOR_UNIQUE": "8up!5f0rrt", "DBSTEST_INQUIRY": "INQUIRY", "DBSTEST_INQUIRY_APPSTR": "Digiflow", "DBSTEST_INQUIRY_UNIQUE": "4!fedL0w", "debugMode": "true", "DefaultEndPoint": "NetTcpBinding_AppService", "DefinitionConfigration": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\WFPages\\Definiton.xml", "DomainName": "http://localhost:3000/", "DynamicHistoryWorker": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\History", "DynamicInboxWorker": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\Inbox", "EducationWebServicePassword": "w17211s", "EducationWebServiceUserName": "WEBSERVICE", "ESSBDurum": "E", "ExMonth": "0", "F2FDeptId": "464", "HedefDegerCanli": "http://pys/PerformansDegerlendirme.aspx?wfInstanceId=", "HedefDegerTest": "http://dtl4sptst1:88/user/PerformansDegerlendirme.aspx?wfInstanceId=", "HedefOnayCanli": "http://pys/hedef_onay.aspx?wfInstanceId=", "HedefOnayTest": "http://dtl4sptst1:88/user/hedef_onay.aspx?wfInstanceId=", "HedefRevOnayCanli": "http://pys/hedef_revizyon_onay.aspx?wfInstanceId=", "HedefRevOnayTest": "http://dtl4sptst1:88/user/hedef_revizyon_onay.aspx?wfInstanceId=", "hibernate.use_reflection_optimizer": "false", "HrServicesEndPointDefaul": "http://dtl1iis4:3331/PersonelBilgisi.asmx", "HrServicesEndPointMedia": "http://dtl1iis4:3335/PersonelBilgisi.asmx", "IsAvansKontrolCalissin": "E", "IsSIDControls": "True", "IsYYSActive": "true", "ITTPTEST_INQUIRY": "INQUIRY", "ITTPTEST_INQUIRY_APPSTR": "Digiflow", "ITTPTEST_INQUIRY_UNIQUE": "4!fedL0w", "imgEmptyFile": "http://dtl4sptst1:889/users/updateYourImage.png", "imgFolder": "http://digiflowtest:889/users/", "imgFolderUpload": "\\\\dtl4sptst1\\users\\", "imgTest": "C:\\Users\\<USER>\\Desktop\\SelfServiceSLN\\SelfServiceHR\\images\\users\\", "KreaSakaryaSirket": "1", "LogicalGroupDefinition": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\WFPages\\LogicalGroups.xml", "noreply_workflow.Mail.FromAddress": "<EMAIL>", "noreply_workflow.Mail.FromAddressPsw": "flow123456+", "noreply_workflow.Mail.FromAddressUserName": "noreply_workflow", "noreply_workflow.Mail.Server": "************", "NotificationTemplateID": "1000", "Odeme_SatinAlmaBaskanLimit": "500000", "OrganisationalSaleRecordBSUrl": "http://test-sdp-lcl.digiturk.net/virtual/basic/OrganisationalSaleRecordBS.svc", "OrganisationalSaleRecordBSUrlApplication": "DIGIPORT", "OrganisationalSaleRecordBSUrlChannelName": "DEFAULT", "OrganisationalSaleRecordBSUrlCompany": "DIGITURK", "OrganisationalSaleRecordBSUrlPassword": "SYSIQ111", "OrganisationalSaleRecordBSUrlUsername": "SYSIQ", "PageTitle": "Digiturk Is Akislari", "PerformansAdminPath": "http://pysadmin/", "RaporMaxTarihDeger": "6", "RaporMaxTarihTanim": "ay", "ReadConfigFilesFromXML": "false", "ServiceName": "ApplicationServer1", "SingleInstance": "true", "SosyalMedyaDocumentId": "2141928", "SosyalMedyaServisi": "http://sdp-lcl.digiturk.net/virtual/basic/PersonalDataProtectionBS.svc?wsdl", "SosyalMedyaServisi2": "http://test-sdp-lcl.digiturk.net/virtual/basic/PersonalDataProtectionBS.svc?wsdl", "StateDefinition": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\WFPages\\StateDefinition.xml", "SUBSET15_INQUIRY": "INQUIRY", "SUBSET15_INQUIRY_APPSTR": "Digiflow", "SUBSET15_INQUIRY_UNIQUE": "4!fedL0w", "SUBSET15_RAPOR": "RAPOR", "SUBSET15_RAPOR_APPSTR": "Rota", "SUBSET15_RAPOR_UNIQUE": "8up!5f0rrt", "TicariCCAddress": "<EMAIL>", "TicariSatisliYayinAcmaccAddress": "<EMAIL>", "TicariSatisliYayinAcmaToAddress": "<EMAIL>", "UzaktanCalismaEndDate": "03.10.2022", "UzaktanCalismaKontrolCalissin": "H", "UzaktanCalismaLimit": "14", "UzaktanCalismaMuvafakatnameTarih": "30.09.2021", "UzaktanCalismaResmiTarih": "19.07.2021", "UzaktanCalismaSirket": "1", "Web.Services.Domain": "DIGITURK", "Web.Services.IsCredentialUsing": "True", "Web.Services.IsProxyUsing": "True", "Web.Services.Password": "Digif16up+-", "Web.Services.ProxyServicesIp": "http://************:8080", "Web.Services.UserName": "Digiflow_sa", "Workflow.Mail.EmbeddedImagesPath": "\\\\dtl1iis3\\Deployment\\MailImages", "Workflow.Mail.FromAddress": "<EMAIL>", "Workflow.Mail.IsMailDebugMode": "True", "Workflow.Mail.LinkDomain": "http://digiflowtest.digiturk.com.tr", "Workflow.Mail.Params": "ad9bBOUpHG1st9IlCOvZA9DCTJKj7XTlewXqZpa4xWo/m0f/ZXwzFpTy9cdYK53Hx2MQqWxlyxSVT5lg5waY6LC3p5i77oc4pHAEGgnKFbAuL48SNlMELo9dIiUOo2RmdTprZ/SAkyKF03+gmRGRexw3+qCFnr/iVOx/58S075o=", "Workflow.Mail.Server": "************", "yayinKurulumProcessType": "57", "yayinKurulumProcessTypeTest": "52", "yayinProcessType": "58", "yayinProcessTypeTest": "53"}, "ServiceSettings": {"IsProxyUsing": true, "IsCredentialUsing": true, "UserName": "Digiflow_sa", "Password": "Digif16up+-", "Domain": "DIGITURK", "ProxyServicesIp": "http://************:8080", "Sharepoint": {"SharePointActionPanelUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ActionPanelDocs/", "SharePointSmsTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/SMSTalepDocs/", "SharePoint3555TalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/3555TalepDocs/", "SharePointBayiTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/BayiTalepDocs/Test/", "SharePointRaporTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/RaporTalepDocs/", "SharePointProjeTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ProjeTalepDocs/", "SharePointKurumsalTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/KurumsalTalepDocs/", "SharePointMakroTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/MakroTalepDocs/", "SharePointOdemeTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/OdemeTalepDocs/", "SharePointOdemeSozlesmeUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/OdemeTalepSozlesmeDocs/", "SharePointReturnRequestUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ReturnRequest/", "SharePointKurumsalIletisimTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/KurumsalIletisimTalepDocs/", "SharePointList": "http://digiflowdocs.digiturk.com.tr/sites/sozlesme/Szleme/", "SharePointFinalList": "http://digiflowdocs.digiturk.com.tr/sites/sozlesme/imzaliSozlesme/", "SharePointFlowList": "http://digiflowdocs.digiturk.com.tr/sites/Workflows/FlowDocs/", "SharePointTransmisyonList": "http://digiflowdocs.digiturk.com.tr/sites/sozlesme/TransmisyonAricaDocs", "SharePointIseGirisFormList": "http://digiflowdocs.digiturk.com.tr/sites/sozlesme/IseGirisFormDocs", "SharePointJobEntranceFormList": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/IseGirisFormuDocs", "SharePointCampaignReqTeklifGorseli": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/CampaignRequestDocs/TeklifGorseli/", "SharePointCampaignReqLastAnalysis": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/CampaignRequestDocs/SonAnalizDocs/", "SharePointOperationalWorkRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/OperationalWorkRequestDocs/", "SharePointConsultantJobEntranceDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ConsultantJobEntranceDocs/", "SharePointProsedurUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ProsedurTalepDocs/", "SharePointBriefRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/BriefRequestDocs/", "SharePointActivityRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ActivityRequestDocs/", "SharePointFaturaDuzenlemeDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/FaturaDuzenlemeDocs/", "SharePointAdHocTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/AdHocTalepDocs/", "SharePointBayiCezaRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/BayiCezaRequestDocs/", "SharePointBayiKesinHesapDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/BayiKesinHesapDocs/", "SharePointRotaRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/RotaRequestDocs/", "SharePointMacroRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/MacroRequestDocs/", "SharePointEkipmanRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/EkipmanRequestDocs/", "SharePointPvrLnbRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/PvrLnbRequestDocs/", "SharePointHavaMuhalefetUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/HavaMuhalefetDocs/", "SharePointTicariFiyatIstisnaUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/TicariUyeFiyatIstisnaDocs/Test/", "SharePointSatisKurulumDegerlendirmeFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/SatisKurulumDegerlendirmeDocs/", "SharePointTsMalzemeBirimFiyat": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/TsMalzemeBirimFiyatDocs/Test/", "SharePointHurdaIhaleDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/HurdaIhaleDocs/Test/", "SharePointPersonelUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/PersonelTalepDocs/test/", "SharePointSatisCiroHesaplamaFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/SatisCiroHesaplamaDocs/test/", "SharePointBiFikrimVarDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/BiFikrimVarDocs/", "SharePointProsedurler": "http://digiflowdocs.digiturk.com.tr/DIGITURK%20Formlar/Prosed%C3%BCrler/", "SharePointProsedurler2": "file:\\\\belgeler\\Digiflowdocs\\ProsedurTalepDocs\\", "ProsedurUploadFolderURL": "http://digiflowtest/ProsedurTemp/", "ProsedurUploadFolderFile": "C:/ProsedurTemp/", "SharePointInstallationDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/SharePointInstallationDocs/"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.Authentication": "Debug", "Microsoft.AspNetCore.Authorization": "Debug", "DigiflowAPI.WebApi.Middlewares": "Debug", "Microsoft.EntityFrameworkCore": "Warning", "System": "Warning", "DigiflowAPI": "Debug"}}, "WriteTo": [{"Name": "File", "Args": {"path": "Logs/digiflow-detailed-.txt", "rollingInterval": "Day", "restrictedToMinimumLevel": "Debug", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "Logs/digiflow-mobile-.txt", "rollingInterval": "Day", "restrictedToMinimumLevel": "Debug", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] Mobile: {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithEnvironmentName"]}}