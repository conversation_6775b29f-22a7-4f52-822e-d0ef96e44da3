import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Shield, Home, ArrowLeft } from 'lucide-react'
import './UnauthorizedComponent.css'

interface UnauthorizedComponentProps {
  message?: string
  showBackButton?: boolean
  showHomeButton?: boolean
}

const UnauthorizedComponent: React.FC<UnauthorizedComponentProps> = ({ message, showBackButton = true, showHomeButton = true }) => {
  const { t } = useTranslation('unauthorized')
  const navigate = useNavigate()

  const handleGoBack = () => {
    navigate(-1)
  }

  const handleGoHome = () => {
    navigate('/')
  }

  return (
    <div className="unauthorized-container">
      <div className="unauthorized-content">
        <div className="unauthorized-icon">
          <Shield size={80} />
        </div>

        <h1 className="unauthorized-title">401</h1>
        <h2 className="unauthorized-subtitle">{t('subtitle')}</h2>

        <p className="unauthorized-message">{message || t('defaultMessage')}</p>

        <div className="unauthorized-actions">
          {showBackButton && (
            <button className="unauthorized-button unauthorized-button-secondary" onClick={handleGoBack}>
              <ArrowLeft size={20} />
              <span>{t('backButton')}</span>
            </button>
          )}

          {showHomeButton && (
            <button className="unauthorized-button unauthorized-button-primary" onClick={handleGoHome}>
              <Home size={20} />
              <span>{t('homeButton')}</span>
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default UnauthorizedComponent
