import React from 'react'
import { WGrid } from 'wface'
import { useTranslation } from 'react-i18next'
import { LanguageSelect } from '@/components/formElements'
import { useDigiflow } from '@/contexts/DigiflowContext'
import useMediaQuery from '@/hooks/useMediaQuery'
import { CSSProperties } from 'react'

const TopButtons: React.FC = () => {
  const { t } = useTranslation(['common'])
  const { isSysAdmin } = useDigiflow()
  const isMobile = useMediaQuery('(max-width: 901px)')

  const styles = {
    outerGrid: {
      display: 'flex',
      width: '100%',
      alignItems: 'flex-end',
      marginRight: isMobile ? 2 : 5,
      flex: 1,
    } as CSSProperties,
    innerGrid: {
      display: 'flex',
      position: 'absolute',
      top: isMobile ? 11 : 6,
      right: isMobile ? 38 : 37,
      zIndex: 99999,
      alignItems: 'center',
      fontWeight: '500',
      gap: isMobile ? '8px' : '12px',
    } as CSSProperties,
    button: {
      backgroundColor: 'white',
      padding: isMobile ? '5px 8px' : '8px 12px',
      borderRadius: '6px',
      cursor: 'pointer',
      border: '1px solid #e2e8f0',
      fontSize: isMobile ? '13px' : '14px',
      fontWeight: 500,
      color: '#1a202c',
      transition: 'all 0.2s ease',
      whiteSpace: 'nowrap',
      '&:hover': {
        backgroundColor: '#f8fafc',
        borderColor: '#cbd5e1',
      },
    } as CSSProperties,
  }

  return (
    <WGrid md={2} my={0.5} item style={styles.outerGrid}>
      <WGrid md={2} item gap={0.5} style={styles.innerGrid}>
        <LanguageSelect
          languages={[
            { code: 'tr', name: t('tr') },
            { code: 'en', name: t('en') },
          ]}
        />

        <button style={styles.button} onClick={() => window.open('http://digiport/default.aspx', '_blank')}>
          {t('digiport')}
        </button>

        {isSysAdmin && (
          <button style={styles.button} onClick={() => window.open('/react/main/test', '_blank')}>
            {t('testScreen')}
          </button>
        )}
      </WGrid>
    </WGrid>
  )
}

export default TopButtons
