{"technology": "TECHNOLOGY", "administrative_processes": "Administrative Processes", "human_resources_processes": "Human Resources Processes", "legal_processes": "Legal Processes", "financial_processes": "Financial Processes", "active_user": "Active User", "workflows_system": "Workflows System", "description": "Description", "create_request": "Create Request", "history": "History", "command_list": "Command List", "inbox": "Inbox", "suspended_inbox": "Suspended Inbox", "end_delegation": "Cancel Delegation", "end_monitoring": "Cancel Monitoring", "request_delegation": "Request Delegation", "request_monitoring": "Request Monitoring", "workflow_management": "Workflow Management", "tr": "Turkish", "en": "English", "department": "Department", "division": "Division", "unit": "Unit", "team": "Team", "user": "User", "workflow": "Workflow", "start_date": "Starting Date", "end_date": "End Date", "required_field": "This field cannot be empty", "workflow_name": "Workflow Name", "owner": "Owner", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "state": "State", "forwarder": "Forwarder", "date": "Date", "bring": "Bring", "users": "Users", "accept_or_reject": "Accept/Reject", "suspend_or_resume": "Suspend/Resume", "accept": "Accept", "reject": "Reject", "forward": "Forward", "post_comment": "Post Comment", "suspend": "Suspend", "resume": "Resume", "cancel": "Cancel", "confirm": "Confirm", "add_comment": "Add Comment", "operation": "Operation", "description_proposal": "Description Proposal", "edit_description": "Edit Description", "testScreen": "Test Screen", "search_placeholder": "Search workflows, tasks...", "search_placeholder_mobile": "Search...", "notifications": "Notifications", "settings": "Settings", "profile": "Profile", "user_menu": "User <PERSON>u", "menu": "<PERSON><PERSON>", "navigation": "Navigation", "choose_destination": "Choose your destination", "workflow_management_title": "DigiFlow", "workflow_management_subtitle": "Workflow Management", "enhanced_components": "Enhanced DigiFlow UI Components", "modern_design_showcase": "A showcase of our enhanced UI components with modern design", "learn_more": "Learn More", "get_started": "Get Started", "explore": "Explore", "save_changes": "Save Changes", "submit_form": "Submit Form", "create_new": "Create New", "download": "Download", "approved": "Approved", "delete": "Delete", "processing": "Processing...", "default_card": "De<PERSON>ult Card", "elevated_card": "Elevated Card", "gradient_card": "Gradient Card", "interactive_card": "Interactive Card", "ghost_card": "Ghost Card", "standard_card_desc": "Standard card with subtle shadow", "enhanced_shadow_desc": "Enhanced shadow for prominence", "purple_gradient_desc": "Purple gradient background", "interactive_desc": "Click me! Cards can be interactive with hover effects", "transparent_desc": "Transparent background for subtle content", "interactive_content": "Interactive cards respond to user interactions with smooth animations and visual feedback.", "ghost_content": "Ghost cards blend into the background while maintaining structure.", "button_components": "Button Components", "modern_buttons_desc": "Modern, accessible buttons with various styles and states", "primary_actions": "Primary Actions", "secondary_actions": "Secondary Actions", "states_special": "States & Special", "showing": "Showing", "of": "of", "results": "results", "rows_per_page": "Rows per page", "loading": "Loading", "no_data": "No data available", "yes": "Yes", "no": "No", "ok": "Ok", "close": "Close", "digiport": "Digiport"}