{"title": "Test Screen", "selectWorkflow": "Select Workflow", "selectUser": "Select User", "historyUpdateByUser": "Update History by User", "instanceId": "WfInstanceId", "historyUpdateByInstance": "Update History by WfInstanceId", "startButton": "Start Test", "userNotSelected": "Please select a user", "historyUpdated": "History updated successfully", "historyUpdateFailed": "Failed to update history", "instanceIdNotEntered": "Please enter an WfInstanceId", "workflows": {"contract": "Contract Request", "bifikrimvar": "BiFikrimVar", "delegation": "Delegation", "monitoring": "Monitoring", "inbox": "Inbox", "history": "History"}}