{"technology": "TEKNOLOJİ", "administrative_processes": "Y<PERSON><PERSON>el Süreçler", "human_resources_processes": "İnsan Kaynakları Süreçleri", "legal_processes": "<PERSON><PERSON><PERSON>", "financial_processes": "Finansal Süreçler", "active_user": "<PERSON><PERSON><PERSON>", "workflows_system": "İş Akışları Sistemi", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "create_request": "Talep Oluştur", "history": "İşlem Geçmişi", "command_list": "İşlem Listesi", "inbox": "Üzerimdeki İşlemler", "suspended_inbox": "<PERSON><PERSON><PERSON>", "end_delegation": "Delegasyon İptali", "end_monitoring": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "request_delegation": "Delegasyon <PERSON>", "request_monitoring": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "workflow_management": "İş Akışı Yönetimi", "tr": "Türkçe", "en": "İngilizce", "department": "<PERSON><PERSON><PERSON>", "division": "Bölüm", "unit": "<PERSON><PERSON><PERSON>", "team": "Takım", "user": "Kullanıcı", "workflow": "İş Akışı", "start_date": "Başlangıç <PERSON>", "end_date": "Bitiş Tarihi", "required_field": "B<PERSON> <PERSON> Geçilemez", "workflow_name": "İş Akışı Adı", "owner": "<PERSON><PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "currency": "<PERSON><PERSON><PERSON>", "state": "<PERSON><PERSON><PERSON>", "forwarder": "<PERSON><PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "bring": "Getir", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "accept_or_reject": "Onay/Red", "suspend_or_resume": "Beklet/Devam <PERSON>", "accept": "<PERSON><PERSON>", "reject": "Red", "forward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "post_comment": "<PERSON><PERSON><PERSON>", "suspend": "Beklet", "resume": "<PERSON><PERSON>", "cancel": "İptal Et", "confirm": "<PERSON><PERSON><PERSON>", "add_comment": "<PERSON><PERSON>", "operation": "İşlem", "description_proposal": "Açıklama Öneri", "edit_description": "Açıklama Düzeltme", "testScreen": "Test Ekranı", "search_placeholder": "İş akışlarını, görevleri ara...", "search_placeholder_mobile": "Ara...", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "profile": "Profil", "user_menu": "Kullanıcı Menüsü", "menu": "<PERSON><PERSON>", "navigation": "Navigasyon", "choose_destination": "Gitmek istediğiniz yeri se<PERSON>", "workflow_management_title": "DigiFlow", "workflow_management_subtitle": "İş Akışı Yönetimi", "enhanced_components": "Gelişmiş DigiFlow UI Bileşenleri", "modern_design_showcase": "Modern tasarımlı gelişmiş UI bileşenlerimizin vitrin alanı", "learn_more": "<PERSON><PERSON>", "get_started": "Başla", "explore": "Keşfet", "save_changes": "Değişiklikleri Kaydet", "submit_form": "<PERSON><PERSON>", "create_new": "<PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "approved": "Onaylandı", "delete": "Sil", "processing": "İşleniyor...", "default_card": "Varsayılan Kart", "elevated_card": "Yükseltilmiş <PERSON>", "gradient_card": "<PERSON><PERSON>", "interactive_card": "Etkileşim<PERSON> Ka<PERSON>", "ghost_card": "<PERSON><PERSON>", "standard_card_desc": "<PERSON><PERSON><PERSON> g<PERSON> standart kart", "enhanced_shadow_desc": "Belirginlik için gel<PERSON> gölge", "purple_gradient_desc": "<PERSON><PERSON> g<PERSON>yan arka plan", "interactive_desc": "Tıkla beni! Kartlar hover efektleri ile etkileşimli olabilir", "transparent_desc": "İnce içerik için şeffaf arka plan", "interactive_content": "Etkileş<PERSON><PERSON> kart<PERSON>, pürüzsüz animasyonlar ve görsel geri bildirimle kullanıcı etkileşimlerine yanıt verir.", "ghost_content": "Hayalet kartlar yapıyı koruyarak arka plana karışır.", "button_components": "<PERSON>on Bileş<PERSON>leri", "modern_buttons_desc": "Çeşitli stillerde ve durumlarda modern, erişilebilir but<PERSON>", "primary_actions": "B<PERSON>ncil <PERSON>", "secondary_actions": "<PERSON><PERSON><PERSON>l <PERSON>ler", "states_special": "<PERSON><PERSON><PERSON> ve <PERSON>", "showing": "Gösterilen", "of": "/", "results": "<PERSON><PERSON><PERSON>", "rows_per_page": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>", "loading": "Yükleniyor", "no_data": "<PERSON><PERSON> bulunamadı", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "ok": "<PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "digiport": "Digiport"}